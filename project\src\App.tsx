import React, { useState } from 'react';
import { Search, BookOpen, User, Home, BarChart3, MessageSquare, Play, Download, CheckCircle, Clock, Star, GraduationCap, Mail, Phone, Zap, Cpu, Settings, Wrench, Shield, Thermometer, Coil, ToggleLeft, Battery, Lightbulb, Microchip, Radio, Power, Gauge } from 'lucide-react';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [selectedTopic, setSelectedTopic] = useState(null);

  const courses = [
    {
      id: 1,
      title: "Introduction to Medical Devices",
      level: "Bachelor's",
      progress: 65,
      duration: "8 weeks",
      description: "Comprehensive introduction to medical devices, their classification, and fundamental principles.",
      instructor: "Dr. <PERSON>",
      rating: 4.8,
      modules: [
        {
          id: 1,
          title: "Fundamentals of Medical Devices",
          topics: [
            {
              id: 1,
              title: "Introduction to Biomedical Engineering",
              completed: true,
              duration: "45 min",
              type: "video",
              summary: "Overview of biomedical engineering field, applications, and career opportunities."
            },
            {
              id: 2,
              title: "Classification of Medical Devices",
              completed: true,
              duration: "30 min",
              type: "reading",
              summary: "Understanding device classification systems, risk categories, and regulatory requirements."
            },
            {
              id: 3,
              title: "Safety Standards and Regulations",
              completed: false,
              duration: "40 min",
              type: "interactive",
              summary: "International safety standards, FDA regulations, and compliance requirements."
            }
          ]
        },
        {
          id: 2,
          title: "Diagnostic Equipment",
          topics: [
            {
              id: 4,
              title: "Medical Imaging Systems",
              completed: false,
              duration: "60 min",
              type: "video",
              summary: "X-ray, MRI, ultrasound, and CT scan technologies and their applications."
            },
            {
              id: 5,
              title: "Biosensors and Monitoring",
              completed: false,
              duration: "50 min",
              type: "lab",
              summary: "Blood glucose monitors, vital signs monitoring, and wearable devices."
            }
          ]
        }
      ]
    },
    {
      id: 2,
      title: "Biomedical Signal Processing",
      level: "Bachelor's",
      progress: 30,
      duration: "10 weeks",
      description: "Advanced signal processing techniques for biomedical applications.",
      instructor: "Prof. Michael Chen",
      rating: 4.9,
      modules: []
    },
    {
      id: 3,
      title: "Medical Device Fundamentals",
      level: "Diploma",
      progress: 80,
      duration: "6 weeks",
      description: "Basic principles of medical devices for diploma-level students.",
      instructor: "Dr. Emma Wilson",
      rating: 4.7,
      modules: []
    },
    {
      id: 4,
      title: "Essential Lab - Electronics",
      titleAr: "المختبر الأساسي - الإلكترونيات",
      level: "Diploma",
      progress: 15,
      duration: "12 weeks",
      description: "Comprehensive electronics fundamentals course covering essential components, testing procedures, and practical applications in Arabic.",
      descriptionAr: "دورة شاملة في أساسيات الإلكترونيات تغطي المكونات الأساسية وإجراءات الاختبار والتطبيقات العملية باللغة العربية.",
      instructor: "Dr. Mohammed Yagoub Esmail",
      rating: 4.9,
      modules: [
        {
          id: 1,
          title: "Electronics Fundamentals",
          titleAr: "أساسيات الإلكترونيات",
          topics: [
            {
              id: 1,
              title: "Introduction to Basic Electronics",
              titleAr: "مقدمة في الإلكترونيات الأساسية",
              completed: true,
              duration: "60 min",
              type: "reading",
              summary: "Basic concepts: Current, Voltage, Resistance, Ohm's Law, AC/DC, and Circuit Types.",
              summaryAr: "المفاهيم الأساسية: التيار الكهربائي، الجهد الكهربائي، المقاومة، قانون أوم، التيار المتردد والمستمر، وأنواع الدوائر.",
              content: {
                concepts: [
                  "التيار الكهربائي: هو تدفق الإلكترونات في موصل",
                  "الجهد الكهربائي: هو القوة الدافعة التي تسبب تدفق التيار",
                  "المقاومة: هي خاصية المادة التي تعيق تدفق التيار",
                  "قانون أوم: العلاقة الأساسية بين الجهد (V)، التيار (I)، والمقاومة (R) وهي V = IR",
                  "الدوائر الكهربائية: المسار المغلق الذي يسمح بتدفق التيار، وتنقسم إلى دوائر التوالي ودوائر التوازي",
                  "التيار المتردد (AC) والتيار المستمر (DC): شرح الفرق بينهما وتطبيقات كل منهما"
                ]
              }
            },
            {
              id: 2,
              title: "Essential Tools for Electronics Repair",
              titleAr: "الأدوات الأساسية المطلوبة في إصلاح الإلكترونيات",
              completed: true,
              duration: "45 min",
              type: "video",
              summary: "Complete guide to essential tools: Multimeter, Soldering Iron, Wire Strippers, and more.",
              summaryAr: "دليل شامل للأدوات الأساسية: الملتيميتر، مكواة اللحام، قاطعة الأسلاك، وأكثر.",
              content: {
                tools: [
                  "الملتيميتر (Multimeter): أداة أساسية لقياس الجهد، التيار، والمقاومة",
                  "مكواة اللحام (Soldering Iron): تستخدم لإنشاء توصيلات كهربائية قوية بين المكونات",
                  "مضخة شفط اللحام (Desoldering Pump) أو شريط إزالة اللحام (Solder Wick): لإزالة اللحام عند الحاجة",
                  "قاطعة أسلاك (Wire Strippers/Cutters): لقطع وتعرية الأسلاك",
                  "مجموعة مفكات دقيقة (Precision Screwdrivers): للتعامل مع البراغي الصغيرة في الأجهزة الإلكترونية",
                  "ملقط (Tweezers): للتعامل مع المكونات الإلكترونية الصغيرة",
                  "مصدر طاقة مكتبي (Bench Power Supply): لتوفير جهد مستمر قابل للتعديل لاختبار الدوائر",
                  "راسم الإشارة (Oscilloscope): لعرض أشكال الموجات الكهربائية وتحليلها"
                ]
              }
            }
          ]
        },
        {
          id: 2,
          title: "Protection Components",
          titleAr: "مكونات الحماية",
          topics: [
            {
              id: 3,
              title: "Fuses",
              titleAr: "الفيوزات",
              completed: false,
              duration: "30 min",
              type: "lab",
              summary: "Understanding fuses: definition, purpose, failure modes, and testing procedures.",
              summaryAr: "فهم الفيوزات: التعريف، الغرض، أوضاع الفشل، وإجراءات الاختبار.",
              content: {
                definition: "سلك رفيع مصمم لينصهر ويقطع الدائرة عند مرور تيار عالٍ جدًا",
                purpose: "حماية الدوائر الإلكترونية من التلف الناتج عن التيار الزائد",
                failureMode: "الدائرة المفتوحة (Open Circuit) بعد انصهاره",
                testing: "باستخدام الملتيميتر على وضع الاستمرارية (Continuity) أو المقاومة، يجب أن يعطي مقاومة قريبة من الصفر إذا كان سليمًا، ومقاومة لا نهائية (دائرة مفتوحة) إذا كان تالفًا"
              }
            },
            {
              id: 4,
              title: "Metal Oxide Varistor (MOV)",
              titleAr: "مقاوم متغير بالأكسيد المعدني",
              completed: false,
              duration: "35 min",
              type: "interactive",
              summary: "MOV components: voltage-dependent resistors for surge protection.",
              summaryAr: "مكونات MOV: مقاومات تعتمد على الجهد لحماية من الارتفاع المفاجئ.",
              content: {
                definition: "مكون تتغير مقاومته بشكل كبير مع تغير الجهد",
                purpose: "حماية الدوائر من ارتفاع الجهد المفاجئ (Voltage Spikes)",
                failureMode: "قصر الدائرة (Short Circuit) بعد تعرضه لجهد عالٍ جدًا، مما يؤدي غالبًا إلى احتراق الفيوز",
                testing: "في حالته الطبيعية، يجب أن يظهر مقاومة عالية جدًا (لا نهائية) عند قياسها بالملتيميتر. إذا أظهر مقاومة منخفضة، فهو تالف"
              }
            },
            {
              id: 5,
              title: "Thermistors",
              titleAr: "الثرمستورات",
              completed: false,
              duration: "40 min",
              type: "lab",
              summary: "Temperature-dependent resistors: PTC and NTC types, applications, and testing.",
              summaryAr: "المقاومات المعتمدة على درجة الحرارة: أنواع PTC و NTC، التطبيقات، والاختبار.",
              content: {
                definition: "مقاوم تتغير قيمته بتغير درجة الحرارة",
                types: {
                  ptc: "PTC (Posistor): تزداد مقاومته مع ارتفاع درجة الحرارة",
                  ntc: "NTC: تقل مقاومته مع ارتفاع درجة الحرارة"
                },
                purpose: "حماية من التيار الزائد (PTC)، أو قياس درجة الحرارة (NTC)",
                failureMode: "دائرة مفتوحة أو قصر في الدائرة، أو تغير في خصائصه الحرارية",
                testing: "قياس مقاومته في درجة حرارة الغرفة ومقارنتها بالقيمة المذكورة في المواصفات. يمكن تسخينه بحذر لملاحظة تغير المقاومة"
              }
            }
          ]
        },
        {
          id: 3,
          title: "Passive Components",
          titleAr: "المكونات السلبية",
          topics: [
            {
              id: 6,
              title: "Inductors",
              titleAr: "المحاثات",
              completed: false,
              duration: "35 min",
              type: "reading",
              summary: "Inductors: energy storage in magnetic fields, applications, and testing methods.",
              summaryAr: "المحاثات: تخزين الطاقة في المجالات المغناطيسية، التطبيقات، وطرق الاختبار.",
              content: {
                definition: "ملف من سلك معزول، غالبًا ما يكون ملفوفًا حول قلب مغناطيسي",
                purpose: "تخزين الطاقة في مجال مغناطيسي، وتستخدم في دوائر الترشيح وإمدادات الطاقة",
                failureMode: "دائرة مفتوحة (انقطاع في الملف) أو قصر في الدائرة (انهيار العزل بين اللفات)",
                testing: "باستخدام الملتيميتر على وضع الاستمرارية، يجب أن تظهر مقاومة منخفضة جدًا (قريبة من الصفر). إذا كانت القراءة لا نهائية، فالمحث تالف. يمكن استخدام جهاز LCR Meter لقياس الحث بدقة"
              }
            },
            {
              id: 7,
              title: "Relays",
              titleAr: "المرحلات",
              completed: false,
              duration: "45 min",
              type: "lab",
              summary: "Electromagnetic switches: operation principles, applications, and testing procedures.",
              summaryAr: "المفاتيح الكهرومغناطيسية: مبادئ التشغيل، التطبيقات، وإجراءات الاختبار.",
              content: {
                definition: "مفتاح كهروميكانيكي يتم التحكم فيه عن طريق ملف كهرومغناطيسي",
                purpose: "استخدام جهد منخفض للتحكم في دائرة ذات جهد عالٍ",
                failureMode: "فشل الملف في توليد المجال المغناطيسي، أو تآكل نقاط التلامس مما يمنع التوصيل الجيد",
                testing: "اختبار مقاومة الملف (يجب أن تكون قيمة محددة وليست صفرًا أو لا نهائية)، وتطبيق الجهد المناسب على الملف والاستماع لصوت النقر مع قياس الاستمرارية بين نقاط التلامس"
              }
            },
            {
              id: 8,
              title: "Capacitors",
              titleAr: "المكثفات",
              completed: false,
              duration: "50 min",
              type: "interactive",
              summary: "Energy storage components: types, markings, discharge procedures, and testing.",
              summaryAr: "مكونات تخزين الطاقة: الأنواع، العلامات، إجراءات التفريغ، والاختبار.",
              content: {
                definition: "مكون يخزن الطاقة الكهربائية في مجال كهربائي",
                types: "إلكتروليتية، سيراميكية، فيلم، تانتالوم",
                uses: "ترشيح، اقتران الإشارات، تخزين الطاقة",
                markings: "فهم رموز الجهد والسعة والتفاوت المسموح به",
                safety: "شرح أهمية وخطوات تفريغ المكثفات الكبيرة قبل التعامل معها",
                failureMode: "قصر في الدائرة، دائرة مفتوحة، أو انخفاض في السعة",
                testing: "باستخدام ملتيميتر يحتوي على خاصية قياس السعة، أو ملاحظة شحن وتفريغ المكثف على وضع المقاومة. يمكن استخدام جهاز ESR Meter لاختبار المكثفات الإلكتروليتية"
              }
            },
            {
              id: 9,
              title: "Resistors",
              titleAr: "المقاومات",
              completed: false,
              duration: "40 min",
              type: "reading",
              summary: "Current limiting components: types, color codes, series/parallel connections, and testing.",
              summaryAr: "مكونات تحديد التيار: الأنواع، رموز الألوان، التوصيلات المتسلسلة/المتوازية، والاختبار.",
              content: {
                definition: "مكون يحد من تدفق التيار في الدائرة",
                uses: "تحديد التيار، تقسيم الجهد",
                types: "ثابتة، متغيرة (Potentiometer)",
                colorCode: "شرح كيفية قراءة قيمة المقاومة والتفاوت المسموح به من خلال الأشرطة الملونة",
                connections: "التوصيل على التوالي والتوازي: كيفية حساب المقاومة الكلية في كل حالة",
                failureMode: "دائرة مفتوحة (غالبًا بسبب الحرارة الزائدة) أو تغير قيمتها خارج نطاق التفاوت المسموح به",
                testing: "باستخدام الملتيميتر على وضع المقاومة (Ohm) ومقارنة القراءة بالقيمة المحددة"
              }
            }
          ]
        },
        {
          id: 4,
          title: "Semiconductor Components",
          titleAr: "المكونات شبه الموصلة",
          topics: [
            {
              id: 10,
              title: "Diodes",
              titleAr: "الدايودات",
              completed: false,
              duration: "45 min",
              type: "lab",
              summary: "One-way current flow devices: types, applications, and testing methods.",
              summaryAr: "أجهزة تدفق التيار في اتجاه واحد: الأنواع، التطبيقات، وطرق الاختبار.",
              content: {
                definition: "مكون شبه موصل يسمح بمرور التيار في اتجاه واحد فقط",
                types: {
                  power: "دايودات القدرة (Power Diodes): تستخدم في دوائر التقويم",
                  signal: "دايودات الإشارة (Signal Diodes): لمعالجة الإشارات الصغيرة",
                  zener: "دايود زينر (Zener Diodes): مصمم للعمل في وضع الانحياز العكسي لتنظيم الجهد",
                  led: "الدايود الباعث للضوء (LEDs): يضيء عند مرور التيار من خلاله"
                },
                zenerUses: "منظم للجهد وحماية من الجهد الزائد",
                failureMode: "قصر في الدائرة (يسمح بمرور التيار في كلا الاتجاهين) أو دائرة مفتوحة (لا يسمح بمرور التيار)",
                testing: "باستخدام وضع اختبار الدايود في الملتيميتر، يجب أن يعطي قراءة جهد في اتجاه واحد وقراءة لا نهائية في الاتجاه المعاكس"
              }
            },
            {
              id: 11,
              title: "Transistors",
              titleAr: "الترانزستورات",
              completed: false,
              duration: "50 min",
              type: "interactive",
              summary: "Amplification and switching devices: operation principles and testing procedures.",
              summaryAr: "أجهزة التضخيم والتحويل: مبادئ التشغيل وإجراءات الاختبار.",
              content: {
                definition: "جهاز شبه موصل يستخدم لتضخيم الإشارات الكهربائية أو كـمفتاح إلكتروني",
                uses: "التضخيم، التحويل (Switching)",
                failureMode: "قصر أو دائرة مفتوحة بين أطرافه (القاعدة، المجمع، الباعث). يمكن أن يحدث الفشل بسبب الحرارة الزائدة أو الجهد الزائد",
                testing: "باستخدام وضع اختبار الدايود في الملتيميتر لاختبار الوصلات بين أطراف الترانزستور (B-E و B-C) كما لو كانت دايودات"
              }
            }
          ]
        },
        {
          id: 5,
          title: "Integrated Circuits & Timing",
          titleAr: "الدوائر المتكاملة والتوقيت",
          topics: [
            {
              id: 12,
              title: "Integrated Circuits (ICs)",
              titleAr: "الدوائر المتكاملة",
              completed: false,
              duration: "40 min",
              type: "reading",
              summary: "Complete circuits on silicon chips: pin arrangements and testing methods.",
              summaryAr: "دوائر كاملة على رقائق السيليكون: ترتيب الأطراف وطرق الاختبار.",
              content: {
                definition: "دائرة إلكترونية كاملة مدمجة على شريحة صغيرة من السيليكون",
                pinArrangement: "كيفية تحديد الطرف رقم 1 (غالبًا بوجود نقطة أو شق)",
                testingTips: [
                  "الفحص البصري بحثًا عن شقوق أو علامات احتراق",
                  "التحقق من وجود جهد التشغيل (VCC) والأرضي (GND) باستخدام الملتيميتر",
                  "ملاحظة ارتفاع درجة حرارة الدائرة بشكل غير طبيعي",
                  "الاختبار المتقدم يتطلب معدات متخصصة ومقارنة الإشارات مع ورقة البيانات (Datasheet)"
                ]
              }
            },
            {
              id: 13,
              title: "Crystals and Resonators",
              titleAr: "البلورات والمرينات",
              completed: false,
              duration: "35 min",
              type: "lab",
              summary: "Precise frequency generation: crystal oscillators vs ceramic resonators.",
              summaryAr: "توليد التردد الدقيق: المذبذبات البلورية مقابل المرنانات السيراميكية.",
              content: {
                definition: "مكونات تستخدم لتوليد إشارة تردد دقيقة ومستقرة",
                types: {
                  crystal: "مذبذب بلوري (Crystal Oscillator): يستخدم بلورة كوارتز، وهو دقيق جدًا ومستقر",
                  ceramic: "مرنان سيراميكي (Ceramic Resonator): يستخدم مادة سيراميكية، وهو أقل تكلفة ودقة من البلوري"
                },
                uses: "توفير إشارة الساعة (Clock Signal) للمعالجات الدقيقة والدوائر الرقمية",
                testing: "يتطلب عادةً راسم إشارة (Oscilloscope) للتحقق من وجود التذبذب الصحيح على أطرافه"
              }
            },
            {
              id: 14,
              title: "Voltage Regulators",
              titleAr: "منظمات الجهد",
              completed: false,
              duration: "40 min",
              type: "interactive",
              summary: "Stable voltage output devices: linear regulators and testing procedures.",
              summaryAr: "أجهزة خرج الجهد المستقر: المنظمات الخطية وإجراءات الاختبار.",
              content: {
                definition: "دائرة متكاملة توفر جهد خرج ثابت بغض النظر عن التغيرات في جهد الدخل أو الحمل",
                types: "سلسلة موجبة (مثل 78xx) وسلسلة سالبة (مثل 79xx)",
                uses: "توفير جهد مستقر وموثوق للدوائر الإلكترونية",
                testing: "قياس جهد الدخل وجهد الخرج باستخدام الملتيميتر. يجب أن يكون جهد الخرج ثابتًا وقريبًا جدًا من القيمة المحددة للمنظم (مثلاً، 5 فولت لمنظم 7805)"
              }
            }
          ]
        },
        {
          id: 6,
          title: "Power Systems & Conclusion",
          titleAr: "أنظمة الطاقة والخاتمة",
          topics: [
            {
              id: 15,
              title: "Switch Mode Power Supply (SMPS)",
              titleAr: "مزود الطاقة بوضع التبديل",
              completed: false,
              duration: "55 min",
              type: "video",
              summary: "High-efficiency power conversion: principles, advantages, and applications.",
              summaryAr: "تحويل الطاقة عالي الكفاءة: المبادئ، المزايا، والتطبيقات.",
              content: {
                definition: "نوع من مزودات الطاقة يستخدم منظمًا تبديليًا لتحويل الطاقة الكهربائية بكفاءة عالية",
                operation: "يقوم بتقطيع جهد الدخل (AC أو DC) بتردد عالٍ، ثم يستخدم محولًا ومكثفات ومحثات لتوليد جهد الخرج المطلوب",
                advantages: "كفاءة عالية، حجم ووزن أصغر مقارنة بمزودات الطاقة الخطية",
                disadvantages: "أكثر تعقيدًا ويمكن أن يولد ضوضاء كهربائية"
              }
            },
            {
              id: 16,
              title: "Course Conclusion",
              titleAr: "خاتمة الدورة",
              completed: false,
              duration: "30 min",
              type: "reading",
              summary: "Review of concepts, safety reminders, and practical application encouragement.",
              summaryAr: "مراجعة المفاهيم، تذكيرات السلامة، وتشجيع التطبيق العملي.",
              content: {
                review: "مراجعة سريعة للمفاهيم والمكونات التي تم تناولها",
                safety: "التأكيد على أهمية السلامة عند التعامل مع الدوائر الكهربائية",
                encouragement: "تشجيع المتعلمين على تطبيق ما تعلموه من خلال بناء مشاريع بسيطة"
              }
            }
          ]
        }
      ]
    }
  ];

  const userProgress = {
    totalCourses: 6,
    completedCourses: 2,
    totalHours: 165,
    currentStreak: 7,
    level: "Intermediate"
  };

  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white">
        <h1 className="text-4xl font-bold mb-2">Welcome back, Alex!</h1>
        <p className="text-blue-100 text-lg">Continue your medical engineering journey</p>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Courses</p>
              <p className="text-2xl font-bold text-gray-900">{userProgress.totalCourses}</p>
            </div>
            <BookOpen className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{userProgress.completedCourses}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Study Hours</p>
              <p className="text-2xl font-bold text-purple-600">{userProgress.totalHours}</p>
            </div>
            <Clock className="h-8 w-8 text-purple-500" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Current Streak</p>
              <p className="text-2xl font-bold text-orange-600">{userProgress.currentStreak} days</p>
            </div>
            <Star className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Current Courses */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Courses</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {courses.map((course) => (
            <div key={course.id} className={`bg-white rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300 hover:border-blue-300 ${course.id === 4 ? 'ring-2 ring-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50' : ''}`}>
              {course.id === 4 && (
                <div className="bg-gradient-to-r from-yellow-500 to-orange-500 p-3 rounded-t-xl">
                  <div className="flex items-center justify-center gap-2 text-white">
                    <Zap className="h-5 w-5" />
                    <span className="font-medium">Essential Electronics Lab</span>
                    <Zap className="h-5 w-5" />
                  </div>
                </div>
              )}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {course.id === 4 && <Cpu className="h-5 w-5 text-orange-500" />}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {course.titleAr ? course.titleAr : course.title}
                      </h3>
                    </div>
                    {course.titleAr && (
                      <h4 className="text-sm font-medium text-gray-600 mb-2">{course.title}</h4>
                    )}
                    <p className="text-sm text-gray-600 mb-3">
                      {course.descriptionAr ? course.descriptionAr : course.description}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                      <span className="flex items-center gap-1">
                        <GraduationCap className="h-4 w-4" />
                        {course.level}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {course.duration}
                      </span>
                      <span className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        {course.rating}
                      </span>
                    </div>
                    {course.id === 4 && (
                      <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full w-fit mb-3">
                        <Radio className="h-3 w-3" />
                        <span>16 Electronics Lessons in Arabic</span>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Progress</span>
                    <span className="text-sm font-medium text-blue-600">{course.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${course.progress}%` }}
                    ></div>
                  </div>
                </div>
                
                <button 
                  onClick={() => { setSelectedCourse(course); setActiveTab('course'); }}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Continue Learning
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Completed "Introduction to Biomedical Engineering"</p>
              <p className="text-xs text-gray-500">2 hours ago</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
            <Play className="h-5 w-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Started "Classification of Medical Devices"</p>
              <p className="text-xs text-gray-500">1 day ago</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
            <MessageSquare className="h-5 w-5 text-purple-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Posted in "Medical Devices" discussion</p>
              <p className="text-xs text-gray-500">3 days ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCourse = () => {
    const isArabicCourse = selectedCourse?.titleAr;
    const isElectronicsLab = selectedCourse?.id === 4;

    return (
      <div className="space-y-6">
        {/* Course Header */}
        <div className={`rounded-2xl p-8 text-white ${isElectronicsLab ? 'bg-gradient-to-r from-yellow-600 to-orange-600' : 'bg-gradient-to-r from-blue-600 to-teal-600'}`}>
          <button
            onClick={() => setActiveTab('dashboard')}
            className="text-blue-100 hover:text-white mb-4 flex items-center gap-2"
          >
            ← {isArabicCourse ? 'العودة للوحة التحكم' : 'Back to Dashboard'}
          </button>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">
                {isArabicCourse ? selectedCourse?.titleAr : selectedCourse?.title}
              </h1>
              {isArabicCourse && (
                <h2 className="text-xl text-blue-100 mb-2">{selectedCourse?.title}</h2>
              )}
              <p className="text-blue-100 mb-4">
                {isArabicCourse ? selectedCourse?.descriptionAr : selectedCourse?.description}
              </p>
              <div className="flex items-center gap-6 text-sm">
                <span>{isArabicCourse ? 'المدرس:' : 'Instructor:'} {selectedCourse?.instructor}</span>
                <span>{isArabicCourse ? 'المدة:' : 'Duration:'} {selectedCourse?.duration}</span>
                <span>{isArabicCourse ? 'المستوى:' : 'Level:'} {selectedCourse?.level}</span>
              </div>
            </div>
            {isElectronicsLab && (
              <div className="flex items-center gap-3">
                <Zap className="h-12 w-12 text-yellow-200" />
                <div className="text-right">
                  <p className="text-lg font-bold">Essential Lab</p>
                  <p className="text-sm text-yellow-200">Electronics Course</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Course Progress */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-900">
              {isArabicCourse ? 'تقدم الدورة' : 'Course Progress'}
            </h2>
            <span className="text-sm font-medium text-blue-600">
              {selectedCourse?.progress}% {isArabicCourse ? 'مكتمل' : 'Complete'}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-300 ${isElectronicsLab ? 'bg-gradient-to-r from-yellow-500 to-orange-500' : 'bg-blue-600'}`}
              style={{ width: `${selectedCourse?.progress}%` }}
            ></div>
          </div>
        </div>

        {/* Course Modules */}
        <div className="space-y-6">
          {selectedCourse?.modules.map((module) => (
            <div key={module.id} className={`bg-white rounded-xl border border-gray-200 ${isElectronicsLab ? 'border-orange-200' : ''}`}>
              <div className={`p-6 border-b border-gray-200 ${isElectronicsLab ? 'bg-gradient-to-r from-orange-50 to-yellow-50' : ''}`}>
                <div className="flex items-center gap-3">
                  {isElectronicsLab && (
                    <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{module.id}</span>
                    </div>
                  )}
                  <h3 className="text-lg font-semibold text-gray-900">
                    {isArabicCourse ? `الوحدة ${module.id}: ${module.titleAr || module.title}` : `Module ${module.id}: ${module.title}`}
                  </h3>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {module.topics.map((topic) => (
                    <div key={topic.id} className={`flex items-center justify-between p-4 rounded-lg hover:bg-gray-100 transition-colors ${isElectronicsLab ? 'bg-orange-50 hover:bg-orange-100' : 'bg-gray-50'}`}>
                      <div className="flex items-center gap-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                          topic.completed ? 'bg-green-500' : (isElectronicsLab ? 'bg-orange-300' : 'bg-gray-300')
                        }`}>
                          {topic.completed && <CheckCircle className="h-4 w-4 text-white" />}
                        </div>
                          <div>
                            <h4 className="font-medium text-gray-900">
                              {isArabicCourse ? (topic.titleAr || topic.title) : topic.title}
                            </h4>
                            {isArabicCourse && topic.titleAr && (
                              <p className="text-xs text-gray-600 mb-1">{topic.title}</p>
                            )}
                            <p className="text-sm text-gray-500">{topic.duration} • {topic.type}</p>
                          </div>
                        </div>
                        <button
                          onClick={() => { setSelectedTopic(topic); setActiveTab('topic'); }}
                          className={`font-medium ${isElectronicsLab ? 'text-orange-600 hover:text-orange-700' : 'text-blue-600 hover:text-blue-700'}`}
                        >
                          {isArabicCourse
                            ? (topic.completed ? 'مراجعة' : 'ابدأ')
                            : (topic.completed ? 'Review' : 'Start')
                          }
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    };

  const renderTopic = () => {
    const isArabicContent = selectedTopic?.titleAr;
    const isElectronicsLab = selectedCourse?.id === 4;

    return (
      <div className="space-y-6">
        {/* Topic Header */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <button
            onClick={() => setActiveTab('course')}
            className="text-blue-600 hover:text-blue-700 mb-4 flex items-center gap-2"
          >
            ← {isArabicContent ? 'العودة للدورة' : 'Back to Course'}
          </button>
          <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {isArabicContent ? selectedTopic?.titleAr : selectedTopic?.title}
              </h1>
              {isArabicContent && (
                <h2 className="text-lg text-gray-600 mb-2">{selectedTopic?.title}</h2>
              )}
              <p className="text-gray-600 mb-4">
                {isArabicContent ? selectedTopic?.summaryAr : selectedTopic?.summary}
              </p>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {selectedTopic?.duration}
                </span>
                <span className="flex items-center gap-1 capitalize">
                  <BookOpen className="h-4 w-4" />
                  {selectedTopic?.type}
                </span>
              </div>
            </div>
            {isElectronicsLab && (
              <div className="flex items-center gap-2">
                <Zap className="h-8 w-8 text-yellow-500" />
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-700">Electronics Lab</p>
                  <p className="text-xs text-gray-500">Essential Components</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Topic Content */}
        <div className="bg-white rounded-xl border border-gray-200 p-8">
          <div className={`prose max-w-none ${isArabicContent ? 'text-right' : ''}`} dir={isArabicContent ? 'rtl' : 'ltr'}>
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              {isArabicContent ? 'محتوى الدرس' : 'Content Overview'}
            </h2>

            {selectedTopic?.type === 'video' && (
              <div className="mb-8">
                <div className="bg-gray-900 rounded-lg aspect-video flex items-center justify-center">
                  <div className="text-center text-white">
                    <Play className="h-16 w-16 mx-auto mb-4 opacity-70" />
                    <p>{isArabicContent ? 'سيتم عرض محتوى الفيديو هنا' : 'Video content would be embedded here'}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Electronics Lab Specific Content */}
            {isElectronicsLab && selectedTopic?.content && (
              <div className="space-y-6">
                {/* Basic Concepts */}
                {selectedTopic.content.concepts && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-yellow-500" />
                      المفاهيم الأساسية
                    </h3>
                    <ul className="space-y-3 text-gray-700">
                      {selectedTopic.content.concepts.map((concept, index) => (
                        <li key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                          <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                          <span className="leading-relaxed">{concept}</span>
                        </li>
                      ))}
                    </ul>
                  </section>
                )}

                {/* Tools */}
                {selectedTopic.content.tools && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Wrench className="h-5 w-5 text-gray-600" />
                      الأدوات الأساسية
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedTopic.content.tools.map((tool, index) => (
                        <div key={index} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                          <p className="text-gray-700 leading-relaxed">{tool}</p>
                        </div>
                      ))}
                    </div>
                  </section>
                )}

                {/* Component Details */}
                {(selectedTopic.content.definition || selectedTopic.content.purpose) && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Cpu className="h-5 w-5 text-blue-500" />
                      تفاصيل المكون
                    </h3>
                    <div className="space-y-4">
                      {selectedTopic.content.definition && (
                        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                          <h4 className="font-semibold text-green-800 mb-2">التعريف:</h4>
                          <p className="text-green-700">{selectedTopic.content.definition}</p>
                        </div>
                      )}
                      {selectedTopic.content.purpose && (
                        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <h4 className="font-semibold text-blue-800 mb-2">الغرض:</h4>
                          <p className="text-blue-700">{selectedTopic.content.purpose}</p>
                        </div>
                      )}
                      {selectedTopic.content.failureMode && (
                        <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                          <h4 className="font-semibold text-red-800 mb-2">وضع الفشل الشائع:</h4>
                          <p className="text-red-700">{selectedTopic.content.failureMode}</p>
                        </div>
                      )}
                      {selectedTopic.content.testing && (
                        <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                          <h4 className="font-semibold text-yellow-800 mb-2">الاختبار:</h4>
                          <p className="text-yellow-700">{selectedTopic.content.testing}</p>
                        </div>
                      )}
                    </div>
                  </section>
                )}

                {/* Types */}
                {selectedTopic.content.types && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Settings className="h-5 w-5 text-purple-500" />
                      الأنواع
                    </h3>
                    <div className="space-y-3">
                      {typeof selectedTopic.content.types === 'string' ? (
                        <p className="text-gray-700 p-3 bg-purple-50 rounded-lg">{selectedTopic.content.types}</p>
                      ) : (
                        Object.entries(selectedTopic.content.types).map(([key, value]) => (
                          <div key={key} className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                            <p className="text-purple-700">{value}</p>
                          </div>
                        ))
                      )}
                    </div>
                  </section>
                )}
              </div>
            )}

            {/* Default content for non-electronics topics */}
            {!isElectronicsLab && (
              <div className="space-y-6">
                <section>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Introduction</h3>
                  <p className="text-gray-700 leading-relaxed">
                    This topic covers the fundamental concepts and principles essential for understanding
                    biomedical engineering applications. You'll learn about the interdisciplinary nature
                    of the field and how engineering principles apply to medical solutions.
                  </p>
                </section>

                <section>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Key Concepts</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <span>Biocompatibility and material selection</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <span>Regulatory compliance and safety standards</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <span>Design controls and risk management</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <span>Clinical applications and user requirements</span>
                    </li>
                  </ul>
                </section>

                <section>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Practical Applications</h3>
                  <div className="bg-blue-50 rounded-lg p-6">
                    <p className="text-gray-700">
                      Understanding these concepts is crucial for developing medical devices that are
                      safe, effective, and compliant with international standards. Real-world applications
                      include prosthetics, diagnostic equipment, and therapeutic devices.
                    </p>
                  </div>
                </section>
              </div>
            )}
          </div>
        </div>

        {/* Downloads and Resources */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {isArabicContent ? 'الموارد والتحميلات' : 'Resources & Downloads'}
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Download className="h-5 w-5 text-gray-500" />
                <span className="text-sm font-medium text-gray-900">
                  {isArabicContent ? 'ملخص الدرس PDF' : 'Topic Summary PDF'}
                </span>
              </div>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                {isArabicContent ? 'تحميل' : 'Download'}
              </button>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Download className="h-5 w-5 text-gray-500" />
                <span className="text-sm font-medium text-gray-900">
                  {isArabicContent ? 'شرائح العرض' : 'Presentation Slides'}
                </span>
              </div>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                {isArabicContent ? 'تحميل' : 'Download'}
              </button>
            </div>
            {isElectronicsLab && (
              <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center gap-3">
                  <Download className="h-5 w-5 text-gray-500" />
                  <span className="text-sm font-medium text-gray-900">دليل الاختبار العملي</span>
                </div>
                <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">تحميل</button>
              </div>
            )}
          </div>
        </div>

        {/* Quick Quiz */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {isArabicContent ? 'اختبار سريع' : 'Quick Assessment'}
          </h3>
          <p className="text-gray-600 mb-4">
            {isArabicContent
              ? 'اختبر فهمك لهذا الموضوع من خلال اختبار سريع.'
              : 'Test your understanding of this topic with a quick quiz.'
            }
          </p>
          <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
            {isArabicContent ? 'ابدأ الاختبار' : 'Take Quiz'}
          </button>
        </div>
      </div>
    );
  };

  const renderProfile = () => (
    <div className="space-y-6">
      {/* Profile Header */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center gap-6">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
            <User className="h-10 w-10 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Alex Thompson</h1>
            <p className="text-gray-600">Biomedical Engineering Student</p>
            <p className="text-sm text-gray-500">Level: {userProgress.level}</p>
          </div>
        </div>
      </div>

      {/* Academic Progress */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Academic Progress</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-600 mb-2">Course Completion</h3>
            <div className="space-y-2">
              {courses.map((course) => (
                <div key={course.id} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{course.title}</span>
                  <span className="text-sm font-medium text-blue-600">{course.progress}%</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-600 mb-2">Achievements</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-700">First Course Completed</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-700">7-Day Study Streak</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-700">Quiz Master</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Settings */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Settings</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
            <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option>English</option>
              <option>Arabic</option>
              <option>French</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email Notifications</label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" defaultChecked />
              <span className="text-sm text-gray-700">Course updates and announcements</span>
            </label>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Study Reminders</label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" defaultChecked />
              <span className="text-sm text-gray-700">Daily study reminders</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900">MedEng Learn</h1>
            </div>
            
            {/* Search Bar */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search courses, topics..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* User Menu */}
            <div className="flex items-center gap-4">
              <button className="p-2 text-gray-500 hover:text-gray-700">
                <MessageSquare className="h-5 w-5" />
              </button>
              <button className="p-2 text-gray-500 hover:text-gray-700">
                <BarChart3 className="h-5 w-5" />
              </button>
              <button 
                onClick={() => setActiveTab('profile')}
                className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
              >
                <User className="h-5 w-5 text-blue-600" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                activeTab === 'dashboard'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Home className="h-4 w-4" />
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab('courses')}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                activeTab === 'courses'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <BookOpen className="h-4 w-4" />
              Courses
            </button>
            <button
              onClick={() => setActiveTab('progress')}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                activeTab === 'progress'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <BarChart3 className="h-4 w-4" />
              Progress
            </button>
            <button
              onClick={() => {
                const essentialLabCourse = courses.find(course => course.id === 4);
                setSelectedCourse(essentialLabCourse);
                setActiveTab('course');
              }}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                selectedCourse?.id === 4 && activeTab === 'course'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Zap className="h-4 w-4" />
              Essential Lab
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && renderDashboard()}
        {activeTab === 'course' && renderCourse()}
        {activeTab === 'topic' && renderTopic()}
        {activeTab === 'profile' && renderProfile()}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Platform Info */}
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <GraduationCap className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900">MedEng Learn</h3>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Medical Engineering Education Platform designed for comprehensive learning 
                and skill development in biomedical engineering.
              </p>
            </div>

            {/* Developer Info */}
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-4">Developer</h4>
              <div className="space-y-2 text-sm text-gray-600">
                <p className="font-medium">Dr. Mohammed Yagoub Esmail</p>
                <p>SUST - BME</p>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span>+249912867327</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span>+966538076790</span>
                </div>
              </div>
            </div>

            {/* Copyright */}
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-4">Copyright</h4>
              <div className="text-sm text-gray-600 space-y-2">
                <p>© 2025 Dr. Mohammed Yagoub Esmail</p>
                <p>All rights reserved.</p>
                <p className="text-xs text-gray-500">
                  This educational platform is designed to support medical engineering 
                  education and research initiatives.
                </p>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-200 mt-8 pt-6">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <p className="text-xs text-gray-500">
                MedEng Learn - Medical Engineering Education Platform
              </p>
              <p className="text-xs text-gray-500">
                Developed by Dr. Mohammed Yagoub Esmail, SUST-BME © 2025
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;