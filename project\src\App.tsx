import React, { useState } from 'react';
import { Search, BookOpen, User, Home, BarChart3, MessageSquare, Play, Download, CheckCircle, Clock, Star, GraduationCap, Mail, Phone, Zap, Cpu, Settings, Wrench, Shield, Thermometer, Coil, ToggleLeft, Battery, Lightbulb, Microchip, Radio, Power, Gauge, FileText, Clipboard, Target, TrendingUp, AlertTriangle, CheckSquare, Activity, Beaker, Calculator, Eye } from 'lucide-react';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [selectedTopic, setSelectedTopic] = useState(null);

  const courses = [
    {
      id: 1,
      title: "Introduction to Medical Devices",
      level: "Bachelor's",
      progress: 65,
      duration: "8 weeks",
      description: "Comprehensive introduction to medical devices, their classification, and fundamental principles.",
      instructor: "Dr. <PERSON>",
      rating: 4.8,
      modules: [
        {
          id: 1,
          title: "Fundamentals of Medical Devices",
          topics: [
            {
              id: 1,
              title: "Introduction to Biomedical Engineering",
              completed: true,
              duration: "45 min",
              type: "video",
              summary: "Overview of biomedical engineering field, applications, and career opportunities."
            },
            {
              id: 2,
              title: "Classification of Medical Devices",
              completed: true,
              duration: "30 min",
              type: "reading",
              summary: "Understanding device classification systems, risk categories, and regulatory requirements."
            },
            {
              id: 3,
              title: "Safety Standards and Regulations",
              completed: false,
              duration: "40 min",
              type: "interactive",
              summary: "International safety standards, FDA regulations, and compliance requirements."
            }
          ]
        },
        {
          id: 2,
          title: "Diagnostic Equipment",
          topics: [
            {
              id: 4,
              title: "Medical Imaging Systems",
              completed: false,
              duration: "60 min",
              type: "video",
              summary: "X-ray, MRI, ultrasound, and CT scan technologies and their applications."
            },
            {
              id: 5,
              title: "Biosensors and Monitoring",
              completed: false,
              duration: "50 min",
              type: "lab",
              summary: "Blood glucose monitors, vital signs monitoring, and wearable devices."
            }
          ]
        }
      ]
    },
    {
      id: 2,
      title: "Biomedical Signal Processing",
      level: "Bachelor's",
      progress: 30,
      duration: "10 weeks",
      description: "Advanced signal processing techniques for biomedical applications.",
      instructor: "Prof. Michael Chen",
      rating: 4.9,
      modules: []
    },
    {
      id: 3,
      title: "Medical Device Fundamentals",
      level: "Diploma",
      progress: 80,
      duration: "6 weeks",
      description: "Basic principles of medical devices for diploma-level students.",
      instructor: "Dr. Emma Wilson",
      rating: 4.7,
      modules: []
    },
    {
      id: 4,
      title: "Essential Lab - Electronics",
      titleAr: "المختبر الأساسي - الإلكترونيات",
      level: "Diploma",
      progress: 15,
      duration: "12 weeks",
      description: "Comprehensive electronics fundamentals course covering essential components, testing procedures, and practical applications in Arabic.",
      descriptionAr: "دورة شاملة في أساسيات الإلكترونيات تغطي المكونات الأساسية وإجراءات الاختبار والتطبيقات العملية باللغة العربية.",
      instructor: "Dr. Mohammed Yagoub Esmail",
      rating: 4.9,
      modules: [
        {
          id: 1,
          title: "Electronics Fundamentals",
          titleAr: "أساسيات الإلكترونيات",
          topics: [
            {
              id: 1,
              title: "Introduction to Basic Electronics",
              titleAr: "مقدمة في الإلكترونيات الأساسية",
              completed: true,
              duration: "60 min",
              type: "reading",
              summary: "Basic concepts: Current, Voltage, Resistance, Ohm's Law, AC/DC, and Circuit Types.",
              summaryAr: "المفاهيم الأساسية: التيار الكهربائي، الجهد الكهربائي، المقاومة، قانون أوم، التيار المتردد والمستمر، وأنواع الدوائر.",
              content: {
                concepts: [
                  "التيار الكهربائي: هو تدفق الإلكترونات في موصل",
                  "الجهد الكهربائي: هو القوة الدافعة التي تسبب تدفق التيار",
                  "المقاومة: هي خاصية المادة التي تعيق تدفق التيار",
                  "قانون أوم: العلاقة الأساسية بين الجهد (V)، التيار (I)، والمقاومة (R) وهي V = IR",
                  "الدوائر الكهربائية: المسار المغلق الذي يسمح بتدفق التيار، وتنقسم إلى دوائر التوالي ودوائر التوازي",
                  "التيار المتردد (AC) والتيار المستمر (DC): شرح الفرق بينهما وتطبيقات كل منهما"
                ],
                details: {
                  current: "التيار الكهربائي هو تدفق الإلكترونات في موصل، ويقاس بوحدة الأمبير (A)",
                  voltage: "الجهد الكهربائي هو القوة الدافعة التي تسبب تدفق التيار، ويقاس بوحدة الفولت (V)",
                  resistance: "المقاومة هي خاصية المادة التي تعيق تدفق التيار، وتقاس بوحدة الأوم (Ω)",
                  ohmsLaw: "قانون أوم: V = IR حيث V الجهد، I التيار، R المقاومة",
                  circuits: "الدوائر الكهربائية تنقسم إلى: دوائر التوالي (Series) ودوائر التوازي (Parallel)",
                  acdc: "التيار المتردد (AC): يتغير اتجاهه دوريًا، التيار المستمر (DC): يتدفق في اتجاه واحد"
                },
                labExperiment: {
                  title: "تجربة 1: التحقق من قانون أوم وقياس المقاومة",
                  objective: "التحقق من صحة قانون أوم وتعلم كيفية قياس الجهد والتيار والمقاومة",
                  requiredEquipment: [
                    "ملتيميتر رقمي (Digital Multimeter)",
                    "مصدر طاقة متغير (0-12V DC)",
                    "مقاومات مختلفة القيم (100Ω, 220Ω, 470Ω, 1kΩ)",
                    "أسلاك توصيل",
                    "لوحة تجارب (Breadboard)",
                    "مفتاح كهربائي"
                  ],
                  procedure: [
                    "تحضير المعدات والتأكد من سلامتها",
                    "ضبط مصدر الطاقة على 0 فولت قبل التوصيل",
                    "توصيل المقاومة 220Ω على لوحة التجارب",
                    "توصيل مصدر الطاقة مع المقاومة عبر المفتاح",
                    "ضبط الملتيميتر على وضع قياس الجهد (DC Voltage)",
                    "قياس الجهد عبر المقاومة عند جهود مختلفة (3V, 6V, 9V, 12V)",
                    "ضبط الملتيميتر على وضع قياس التيار (DC Current)",
                    "قياس التيار المار عبر المقاومة لكل جهد",
                    "حساب المقاومة باستخدام قانون أوم: R = V/I",
                    "مقارنة النتائج المحسوبة مع القيمة الاسمية للمقاومة",
                    "تكرار التجربة مع مقاومات أخرى"
                  ],
                  safetyPrecautions: [
                    "التأكد من فصل الطاقة قبل تغيير التوصيلات",
                    "عدم تجاوز الحد الأقصى للتيار المسموح للمقاومة",
                    "فحص الأسلاك والتوصيلات قبل تشغيل الدائرة",
                    "استخدام النطاق المناسب في الملتيميتر"
                  ],
                  expectedResults: {
                    description: "يجب أن تكون النتائج متوافقة مع قانون أوم",
                    calculations: "R = V/I يجب أن تعطي قيمة ثابتة تقريباً لكل مقاومة",
                    tolerance: "الاختلاف المقبول ±5% من القيمة الاسمية"
                  },
                  dataSheet: {
                    headers: ["الجهد (V)", "التيار (mA)", "المقاومة المحسوبة (Ω)", "الانحراف (%)"],
                    sampleData: [
                      ["3.0", "13.6", "220.6", "+0.3%"],
                      ["6.0", "27.3", "219.8", "-0.1%"],
                      ["9.0", "40.9", "220.0", "0.0%"],
                      ["12.0", "54.5", "220.2", "+0.1%"]
                    ]
                  }
                }
              }
            },
            {
              id: 2,
              title: "Essential Tools for Electronics Repair",
              titleAr: "الأدوات الأساسية المطلوبة في إصلاح الإلكترونيات",
              completed: true,
              duration: "45 min",
              type: "video",
              summary: "Complete guide to essential tools: Multimeter, Soldering Iron, Wire Strippers, and more.",
              summaryAr: "دليل شامل للأدوات الأساسية: الملتيميتر، مكواة اللحام، قاطعة الأسلاك، وأكثر.",
              content: {
                tools: [
                  "الملتيميتر (Multimeter): أداة أساسية لقياس الجهد، التيار، والمقاومة - يجب أن يكون في كل مختبر إلكترونيات",
                  "مكواة اللحام (Soldering Iron): تستخدم لإنشاء توصيلات كهربائية قوية بين المكونات - درجة الحرارة المثلى 300-350°C",
                  "مضخة شفط اللحام (Desoldering Pump) أو شريط إزالة اللحام (Solder Wick): لإزالة اللحام عند الحاجة للإصلاح",
                  "قاطعة أسلاك (Wire Strippers/Cutters): لقطع وتعرية الأسلاك بدقة دون إتلاف النحاس الداخلي",
                  "مجموعة مفكات دقيقة (Precision Screwdrivers): للتعامل مع البراغي الصغيرة في الأجهزة الإلكترونية",
                  "ملقط (Tweezers): للتعامل مع المكونات الإلكترونية الصغيرة وتجنب لمسها باليد",
                  "مصدر طاقة مكتبي (Bench Power Supply): لتوفير جهد مستمر قابل للتعديل (0-30V) لاختبار الدوائر",
                  "راسم الإشارة (Oscilloscope): لعرض أشكال الموجات الكهربائية وتحليلها - أداة متقدمة للتشخيص"
                ],
                safetyTips: [
                  "تأكد من فصل الطاقة قبل بدء أي عمل",
                  "استخدم نظارات الحماية عند اللحام",
                  "تأكد من التهوية الجيدة في منطقة العمل",
                  "احتفظ بالأدوات نظيفة ومنظمة"
                ]
              }
            }
          ]
        },
        {
          id: 2,
          title: "Protection Components",
          titleAr: "مكونات الحماية",
          topics: [
            {
              id: 3,
              title: "Fuses",
              titleAr: "الفيوزات",
              completed: false,
              duration: "30 min",
              type: "lab",
              summary: "Understanding fuses: definition, purpose, failure modes, and testing procedures.",
              summaryAr: "فهم الفيوزات: التعريف، الغرض، أوضاع الفشل، وإجراءات الاختبار.",
              content: {
                definition: "سلك رفيع مصمم لينصهر ويقطع الدائرة عند مرور تيار عالٍ جدًا",
                purpose: "حماية الدوائر الإلكترونية من التلف الناتج عن التيار الزائد",
                failureMode: "الدائرة المفتوحة (Open Circuit) بعد انصهاره",
                testing: "باستخدام الملتيميتر على وضع الاستمرارية (Continuity) أو المقاومة، يجب أن يعطي مقاومة قريبة من الصفر إذا كان سليمًا، ومقاومة لا نهائية (دائرة مفتوحة) إذا كان تالفًا",
                labExperiment: {
                  title: "تجربة 3: اختبار الفيوزات وتحديد قيمة التيار المقنن",
                  objective: "تعلم كيفية اختبار الفيوزات وفهم خصائص الحماية من التيار الزائد",
                  requiredEquipment: [
                    "ملتيميتر رقمي مع وضع الاستمرارية",
                    "مجموعة فيوزات مختلفة (0.5A, 1A, 2A, 5A)",
                    "فيوزات محترقة للمقارنة",
                    "مصدر طاقة متغير (0-15V)",
                    "مقاومة حمل متغيرة (Rheostat)",
                    "أميتر لقياس التيار",
                    "حامل فيوزات (Fuse Holder)",
                    "أسلاك توصيل"
                  ],
                  procedure: [
                    "الجزء الأول: اختبار الاستمرارية",
                    "ضبط الملتيميتر على وضع الاستمرارية (Continuity)",
                    "اختبار الفيوزات السليمة - يجب سماع صوت التنبيه",
                    "اختبار الفيوزات المحترقة - لا يجب سماع صوت",
                    "تسجيل النتائج في جدول البيانات",
                    "",
                    "الجزء الثاني: اختبار المقاومة",
                    "ضبط الملتيميتر على وضع المقاومة (Ohm)",
                    "قياس مقاومة الفيوزات السليمة (يجب أن تكون قريبة من الصفر)",
                    "قياس مقاومة الفيوزات المحترقة (لا نهائية)",
                    "",
                    "الجزء الثالث: اختبار عملي للحماية",
                    "تركيب دائرة مع فيوز 1A ومقاومة حمل",
                    "زيادة التيار تدريجياً ومراقبة نقطة احتراق الفيوز",
                    "تسجيل قيمة التيار عند احتراق الفيوز",
                    "مقارنة النتيجة مع القيمة المقننة"
                  ],
                  safetyPrecautions: [
                    "فصل الطاقة قبل تغيير الفيوزات",
                    "عدم استخدام فيوزات بقيمة أعلى من المطلوب",
                    "عدم تجاوز الجهد المقنن للفيوز",
                    "ارتداء نظارات الحماية عند اختبار الاحتراق"
                  ],
                  expectedResults: {
                    continuity: "الفيوزات السليمة: استمرارية كاملة، المحترقة: لا استمرارية",
                    resistance: "الفيوزات السليمة: 0.1-0.5Ω، المحترقة: ∞Ω",
                    burnout: "احتراق الفيوز عند 110-150% من التيار المقنن"
                  },
                  dataSheet: {
                    headers: ["نوع الفيوز", "القيمة المقننة", "المقاومة (Ω)", "الاستمرارية", "تيار الاحتراق (A)", "الحالة"],
                    sampleData: [
                      ["زجاجي", "1A", "0.2", "نعم", "1.3", "سليم"],
                      ["سيراميكي", "2A", "0.1", "نعم", "2.4", "سليم"],
                      ["زجاجي", "1A", "∞", "لا", "-", "محترق"],
                      ["سريع", "0.5A", "0.3", "نعم", "0.7", "سليم"]
                    ]
                  }
                }
              }
            },
            {
              id: 4,
              title: "Metal Oxide Varistor (MOV)",
              titleAr: "مقاوم متغير بالأكسيد المعدني",
              completed: false,
              duration: "35 min",
              type: "interactive",
              summary: "MOV components: voltage-dependent resistors for surge protection.",
              summaryAr: "مكونات MOV: مقاومات تعتمد على الجهد لحماية من الارتفاع المفاجئ.",
              content: {
                definition: "مكون تتغير مقاومته بشكل كبير مع تغير الجهد",
                purpose: "حماية الدوائر من ارتفاع الجهد المفاجئ (Voltage Spikes)",
                failureMode: "قصر الدائرة (Short Circuit) بعد تعرضه لجهد عالٍ جدًا، مما يؤدي غالبًا إلى احتراق الفيوز",
                testing: "في حالته الطبيعية، يجب أن يظهر مقاومة عالية جدًا (لا نهائية) عند قياسها بالملتيميتر. إذا أظهر مقاومة منخفضة، فهو تالف",
                labExperiment: {
                  title: "تجربة 4: اختبار مقاوم متغير بالأكسيد المعدني (MOV) وخصائص الحماية",
                  objective: "فهم خصائص MOV وقياس جهد التشغيل واختبار فعالية الحماية من الجهد الزائد",
                  requiredEquipment: [
                    "ملتيميتر رقمي عالي المقاومة الداخلية",
                    "مجموعة MOV مختلفة الجهود (130V, 275V, 385V)",
                    "مصدر جهد متغير عالي (0-500V) مع حماية التيار",
                    "راسم الإشارة لقياس زمن الاستجابة",
                    "مولد نبضات عالية الجهد",
                    "مقاومة حد التيار (1kΩ)",
                    "فولتميتر عالي الجهد",
                    "أسلاك عزل عالي"
                  ],
                  procedure: [
                    "الجزء الأول: قياس المقاومة في الحالة العادية",
                    "ضبط الملتيميتر على أعلى نطاق مقاومة (20MΩ)",
                    "قياس مقاومة MOV في درجة حرارة الغرفة",
                    "تسجيل القراءة (يجب أن تكون عالية جداً >10MΩ)",
                    "تكرار القياس مع MOV مختلفة الجهود",
                    "",
                    "الجزء الثاني: تحديد جهد التشغيل",
                    "توصيل MOV مع مقاومة حد التيار في سلسلة",
                    "زيادة الجهد تدريجياً من 0V بخطوات 10V",
                    "مراقبة التيار المار عبر الدائرة",
                    "تسجيل الجهد عند بداية زيادة التيار بشكل ملحوظ",
                    "هذا هو جهد التشغيل للـ MOV",
                    "",
                    "الجزء الثالث: اختبار الاستجابة للنبضات",
                    "توصيل مولد النبضات مع MOV",
                    "تطبيق نبضة جهد عالي (أعلى من جهد التشغيل)",
                    "قياس زمن الاستجابة باستخدام راسم الإشارة",
                    "ملاحظة انخفاض الجهد عبر MOV أثناء النبضة",
                    "",
                    "الجزء الرابع: اختبار الحماية العملي",
                    "بناء دائرة حماية بسيطة مع MOV",
                    "تطبيق جهد زائد مفاجئ",
                    "مراقبة حماية الدائرة من الجهد الزائد",
                    "قياس الجهد المحدود بواسطة MOV"
                  ],
                  safetyPrecautions: [
                    "استخدام معدات حماية شخصية عند التعامل مع الجهود العالية",
                    "التأكد من عزل جميع التوصيلات",
                    "عدم لمس الدائرة أثناء تطبيق الجهد العالي",
                    "استخدام حماية التيار لمنع تلف MOV",
                    "العمل في منطقة جافة ومعزولة"
                  ],
                  expectedResults: {
                    normalResistance: "مقاومة عالية جداً (>10MΩ) في الحالة العادية",
                    operatingVoltage: "جهد التشغيل يجب أن يطابق القيمة المقننة ±10%",
                    responseTime: "زمن استجابة أقل من 25 نانوثانية",
                    protection: "تحديد الجهد عند القيمة المقننة مع تمرير التيار الزائد",
                    recovery: "عودة إلى المقاومة العالية بعد انتهاء النبضة"
                  },
                  dataSheet: {
                    headers: ["جهد MOV المقنن", "المقاومة العادية (MΩ)", "جهد التشغيل المقاس (V)", "الانحراف (%)", "زمن الاستجابة (ns)", "الحالة"],
                    sampleData: [
                      ["130V", ">10", "142", "+9.2%", "18", "سليم"],
                      ["275V", ">10", "285", "+3.6%", "22", "سليم"],
                      ["385V", ">10", "378", "-1.8%", "20", "سليم"],
                      ["275V", "2.5", "195", "-29%", "45", "تالف"]
                    ]
                  },
                  practicalResults: {
                    title: "النتائج العملية المتوقعة",
                    observations: [
                      "في الحالة العادية: MOV يظهر مقاومة عالية جداً ولا يمرر تيار",
                      "عند الوصول لجهد التشغيل: انخفاض مفاجئ في المقاومة وزيادة في التيار",
                      "أثناء النبضة العالية: MOV يحد الجهد ويحمي الدائرة",
                      "بعد انتهاء النبضة: عودة MOV لحالته الأصلية عالية المقاومة"
                    ],
                    calculations: [
                      "حساب الطاقة المبددة: P = V × I أثناء التشغيل",
                      "حساب معامل عدم الخطية: α = log(I2/I1) / log(V2/V1)",
                      "تقييم فعالية الحماية: نسبة تخفيض الجهد الزائد"
                    ]
                  }
                }
              }
            },
            {
              id: 5,
              title: "Thermistors",
              titleAr: "الثرمستورات",
              completed: false,
              duration: "40 min",
              type: "lab",
              summary: "Temperature-dependent resistors: PTC and NTC types, applications, and testing.",
              summaryAr: "المقاومات المعتمدة على درجة الحرارة: أنواع PTC و NTC، التطبيقات، والاختبار.",
              content: {
                definition: "مقاوم تتغير قيمته بتغير درجة الحرارة",
                types: {
                  ptc: "PTC (Posistor): تزداد مقاومته مع ارتفاع درجة الحرارة",
                  ntc: "NTC: تقل مقاومته مع ارتفاع درجة الحرارة"
                },
                purpose: "حماية من التيار الزائد (PTC)، أو قياس درجة الحرارة (NTC)",
                failureMode: "دائرة مفتوحة أو قصر في الدائرة، أو تغير في خصائصه الحرارية",
                testing: "قياس مقاومته في درجة حرارة الغرفة ومقارنتها بالقيمة المذكورة في المواصفات. يمكن تسخينه بحذر لملاحظة تغير المقاومة",
                labExperiment: {
                  title: "تجربة 5: دراسة خصائص الثرمستورات وتطبيقاتها الحرارية",
                  objective: "فهم سلوك الثرمستورات مع تغير درجة الحرارة وتطبيقها في القياس والحماية",
                  requiredEquipment: [
                    "ملتيميتر رقمي مع دقة عالية",
                    "ثرمستور NTC (10kΩ @ 25°C)",
                    "ثرمستور PTC للحماية",
                    "مقياس حرارة رقمي دقيق",
                    "حمام مائي قابل للتحكم في درجة الحرارة",
                    "مصدر حرارة متدرج (مجفف شعر أو مسدس حراري)",
                    "مصدر طاقة DC متغير",
                    "مقاومة ثابتة 10kΩ للمقارنة",
                    "كرونومتر لقياس زمن الاستجابة"
                  ],
                  procedure: [
                    "الجزء الأول: معايرة NTC مع درجة الحرارة",
                    "قياس مقاومة NTC في درجة حرارة الغرفة (25°C)",
                    "تسجيل القيمة الأساسية والمقارنة مع المواصفات",
                    "تحضير حمام مائي عند درجات حرارة مختلفة (0°C, 10°C, 25°C, 40°C, 60°C, 80°C)",
                    "غمر الثرمستور في كل درجة حرارة لمدة 5 دقائق",
                    "قياس المقاومة عند كل درجة حرارة",
                    "رسم منحنى المقاومة مقابل درجة الحرارة",
                    "",
                    "الجزء الثاني: حساب معامل الحرارة",
                    "استخدام معادلة Steinhart-Hart لحساب معامل β",
                    "β = ln(R1/R2) / (1/T1 - 1/T2)",
                    "حيث R1, R2 المقاومات و T1, T2 درجات الحرارة بالكلفن",
                    "مقارنة النتيجة مع قيمة β المعطاة في المواصفات",
                    "",
                    "الجزء الثالث: اختبار زمن الاستجابة",
                    "نقل الثرمستور من درجة حرارة منخفضة إلى عالية",
                    "قياس الزمن اللازم للوصول إلى 63% من التغيير النهائي",
                    "تسجيل ثابت الزمن الحراري",
                    "",
                    "الجزء الرابع: اختبار PTC للحماية",
                    "توصيل PTC في دائرة مع حمل",
                    "زيادة التيار تدريجياً ومراقبة ارتفاع درجة الحرارة",
                    "ملاحظة زيادة المقاومة وانخفاض التيار عند الحد الحرج",
                    "قياس درجة حرارة التشغيل للحماية"
                  ],
                  safetyPrecautions: [
                    "تجنب التسخين المفرط الذي قد يتلف الثرمستور",
                    "استخدام حماية من الماء عند استخدام الحمام المائي",
                    "عدم تجاوز الحد الأقصى لدرجة الحرارة المسموحة",
                    "التأكد من التبريد التدريجي لتجنب الصدمة الحرارية"
                  ],
                  expectedResults: {
                    ntcBehavior: "انخفاض المقاومة مع زيادة درجة الحرارة بشكل أسي",
                    betaValue: "معامل β يجب أن يطابق المواصفات ±5%",
                    timeConstant: "ثابت الزمن الحراري 10-60 ثانية حسب الحجم",
                    ptcProtection: "زيادة مفاجئة في المقاومة عند درجة حرارة التشغيل",
                    accuracy: "دقة قياس درجة الحرارة ±1°C بعد المعايرة"
                  },
                  dataSheet: {
                    headers: ["درجة الحرارة (°C)", "مقاومة NTC (kΩ)", "مقاومة PTC (Ω)", "معامل β", "زمن الاستجابة (s)", "دقة القياس (°C)"],
                    sampleData: [
                      ["0", "32.65", "1250", "3950", "45", "±0.8"],
                      ["25", "10.00", "1280", "3950", "35", "±0.5"],
                      ["50", "3.60", "1320", "3950", "28", "±0.7"],
                      ["75", "1.42", "1380", "3950", "22", "±0.9"],
                      ["100", "0.61", "2850", "3950", "18", "±1.2"]
                    ]
                  },
                  practicalResults: {
                    title: "التطبيقات العملية والنتائج",
                    temperatureSensor: [
                      "استخدام NTC كمستشعر حرارة دقيق",
                      "معايرة المنحنى للحصول على قراءات دقيقة",
                      "تطبيق في أجهزة قياس درجة حرارة الجسم",
                      "استخدام في أنظمة التحكم الحراري"
                    ],
                    protectionCircuit: [
                      "تطبيق PTC في حماية المحركات من الحرارة الزائدة",
                      "استخدام في دوائر الحماية من التيار الزائد",
                      "تطبيق في أجهزة التدفئة الذاتية التنظيم",
                      "حماية البطاريات من الشحن الزائد"
                    ]
                  }
                }
              }
            }
          ]
        },
        {
          id: 3,
          title: "Passive Components",
          titleAr: "المكونات السلبية",
          topics: [
            {
              id: 6,
              title: "Inductors",
              titleAr: "المحاثات",
              completed: false,
              duration: "35 min",
              type: "reading",
              summary: "Inductors: energy storage in magnetic fields, applications, and testing methods.",
              summaryAr: "المحاثات: تخزين الطاقة في المجالات المغناطيسية، التطبيقات، وطرق الاختبار.",
              content: {
                definition: "ملف من سلك معزول، غالبًا ما يكون ملفوفًا حول قلب مغناطيسي",
                purpose: "تخزين الطاقة في مجال مغناطيسي، وتستخدم في دوائر الترشيح وإمدادات الطاقة",
                failureMode: "دائرة مفتوحة (انقطاع في الملف) أو قصر في الدائرة (انهيار العزل بين اللفات)",
                testing: "باستخدام الملتيميتر على وضع الاستمرارية، يجب أن تظهر مقاومة منخفضة جدًا (قريبة من الصفر). إذا كانت القراءة لا نهائية، فالمحث تالف. يمكن استخدام جهاز LCR Meter لقياس الحث بدقة",
                labExperiment: {
                  title: "تجربة 6: قياس الحث وخصائص المحاثات في الدوائر",
                  objective: "فهم خصائص المحاثات وقياس قيمة الحث وسلوكها في دوائر AC و DC",
                  requiredEquipment: [
                    "جهاز LCR Meter لقياس الحث",
                    "ملتيميتر لقياس المقاومة DC",
                    "مولد إشارة متغير التردد (1Hz - 1MHz)",
                    "راسم الإشارة ثنائي القناة",
                    "مجموعة محاثات مختلفة (1μH - 1H)",
                    "محاثات تالفة للمقارنة",
                    "مقاومة 1kΩ للقياس",
                    "مكثف 1μF لدائرة الرنين",
                    "مصدر طاقة DC للاختبار"
                  ],
                  procedure: [
                    "الجزء الأول: قياس المقاومة DC",
                    "ضبط الملتيميتر على وضع المقاومة",
                    "قياس مقاومة الملف DC لكل محث",
                    "تسجيل القيم (يجب أن تكون منخفضة للمحاثات السليمة)",
                    "مقارنة مع المواصفات المعطاة",
                    "",
                    "الجزء الثاني: قياس قيمة الحث",
                    "استخدام LCR Meter على تردد 1kHz",
                    "قياس قيمة الحث لكل محث",
                    "حساب نسبة الانحراف عن القيمة الاسمية",
                    "اختبار تأثير التردد على قيمة الحث",
                    "",
                    "الجزء الثالث: دراسة الاستجابة الترددية",
                    "توصيل المحث مع مقاومة في دائرة RL",
                    "تطبيق إشارة جيبية متغيرة التردد",
                    "قياس الجهد عبر المحث والمقاومة",
                    "حساب المعاوقة الحثية XL = 2πfL",
                    "رسم منحنى المعاوقة مقابل التردد",
                    "",
                    "الجزء الرابع: دائرة الرنين LC",
                    "توصيل المحث مع مكثف في دائرة رنين",
                    "تحديد تردد الرنين f₀ = 1/(2π√LC)",
                    "قياس تردد الرنين عملياً",
                    "مقارنة النتيجة النظرية مع العملية",
                    "",
                    "الجزء الخامس: اختبار التشبع المغناطيسي",
                    "تطبيق تيار DC متزايد على المحث",
                    "مراقبة تغير قيمة الحث مع زيادة التيار",
                    "تحديد نقطة بداية التشبع المغناطيسي"
                  ],
                  safetyPrecautions: [
                    "تجنب تطبيق تيارات عالية قد تسبب تسخين المحث",
                    "فحص عزل الملف قبل الاختبار",
                    "استخدام ترددات مناسبة لتجنب الرنين غير المرغوب",
                    "مراقبة درجة حرارة المحث أثناء الاختبار"
                  ],
                  expectedResults: {
                    dcResistance: "مقاومة DC منخفضة (0.1Ω - 100Ω حسب الحجم)",
                    inductanceValue: "قيمة الحث ضمن التفاوت المسموح ±20%",
                    frequencyResponse: "زيادة المعاوقة خطياً مع التردد",
                    resonantFrequency: "تردد الرنين يطابق الحساب النظري ±5%",
                    saturation: "انخفاض الحث عند التشبع المغناطيسي"
                  },
                  dataSheet: {
                    headers: ["قيمة الحث الاسمية", "الحث المقاس", "مقاومة DC (Ω)", "تردد الرنين (Hz)", "معامل الجودة Q", "الحالة"],
                    sampleData: [
                      ["100μH", "98μH", "2.5", "15915", "45", "سليم"],
                      ["1mH", "1.05mH", "12.8", "5033", "38", "سليم"],
                      ["10mH", "9.8mH", "45.2", "1592", "28", "سليم"],
                      ["100mH", "∞", "∞", "-", "0", "تالف"]
                    ]
                  },
                  practicalResults: {
                    title: "التطبيقات العملية والنتائج",
                    filterApplications: [
                      "استخدام في مرشحات التمرير المنخفض",
                      "تطبيق في دوائر تنعيم التيار",
                      "استخدام في مرشحات EMI",
                      "تطبيق في دوائر الرنين للاتصالات"
                    ],
                    powerApplications: [
                      "محاثات تخزين الطاقة في محولات SMPS",
                      "خانقات التيار في دوائر القدرة",
                      "محاثات التنعيم في مقومات الطاقة",
                      "ملفات الإشعال في السيارات"
                    ],
                    qualityFactors: [
                      "معامل الجودة Q = XL/R يحدد كفاءة المحث",
                      "Q عالي يعني فقدان طاقة أقل",
                      "تأثير التردد على معامل الجودة",
                      "أهمية Q في دوائر الرنين"
                    ]
                  }
                }
              }
            },
            {
              id: 7,
              title: "Relays",
              titleAr: "المرحلات",
              completed: false,
              duration: "45 min",
              type: "lab",
              summary: "Electromagnetic switches: operation principles, applications, and testing procedures.",
              summaryAr: "المفاتيح الكهرومغناطيسية: مبادئ التشغيل، التطبيقات، وإجراءات الاختبار.",
              content: {
                definition: "مفتاح كهروميكانيكي يتم التحكم فيه عن طريق ملف كهرومغناطيسي",
                purpose: "استخدام جهد منخفض للتحكم في دائرة ذات جهد عالٍ",
                failureMode: "فشل الملف في توليد المجال المغناطيسي، أو تآكل نقاط التلامس مما يمنع التوصيل الجيد",
                testing: "اختبار مقاومة الملف (يجب أن تكون قيمة محددة وليست صفرًا أو لا نهائية)، وتطبيق الجهد المناسب على الملف والاستماع لصوت النقر مع قياس الاستمرارية بين نقاط التلامس"
              }
            },
            {
              id: 8,
              title: "Capacitors",
              titleAr: "المكثفات",
              completed: false,
              duration: "50 min",
              type: "interactive",
              summary: "Energy storage components: types, markings, discharge procedures, and testing.",
              summaryAr: "مكونات تخزين الطاقة: الأنواع، العلامات، إجراءات التفريغ، والاختبار.",
              content: {
                definition: "مكون يخزن الطاقة الكهربائية في مجال كهربائي بين لوحين موصلين مفصولين بمادة عازلة",
                types: {
                  electrolytic: "إلكتروليتية: سعة عالية، قطبية، تستخدم في دوائر الطاقة",
                  ceramic: "سيراميكية: صغيرة الحجم، غير قطبية، للترددات العالية",
                  film: "فيلم: مستقرة، دقيقة، للتطبيقات الحساسة",
                  tantalum: "تانتالوم: سعة عالية، حجم صغير، موثوقية عالية"
                },
                uses: "ترشيح التيار المتردد، اقتران الإشارات، تخزين الطاقة، تنعيم الجهد",
                markings: {
                  capacitance: "السعة: تقاس بالفاراد (F) أو وحداتها الفرعية (μF, nF, pF)",
                  voltage: "جهد التشغيل: الحد الأقصى للجهد المسموح به",
                  tolerance: "التفاوت المسموح: ±5%, ±10%, ±20%",
                  polarity: "القطبية: المكثفات الإلكتروليتية لها قطب موجب وسالب"
                },
                safety: {
                  discharge: "تفريغ المكثفات الكبيرة باستخدام مقاومة عالية القيمة",
                  voltage: "تجنب تجاوز جهد التشغيل المحدد",
                  polarity: "احترام القطبية في المكثفات الإلكتروليتية",
                  storage: "تخزين المكثفات في مكان جاف وبارد"
                },
                failureMode: "قصر في الدائرة، دائرة مفتوحة، انخفاض في السعة، تسرب التيار",
                testing: "قياس السعة بالملتيميتر، اختبار ESR للمكثفات الإلكتروليتية، فحص التسرب",
                labExperiment: {
                  title: "تجربة 8: اختبار المكثفات وقياس السعة والتسرب",
                  objective: "تعلم طرق اختبار المكثفات المختلفة وقياس خصائصها الكهربائية",
                  requiredEquipment: [
                    "ملتيميتر مع وظيفة قياس السعة",
                    "جهاز ESR Meter للمكثفات الإلكتروليتية",
                    "مجموعة مكثفات مختلفة (إلكتروليتية، سيراميكية، فيلم)",
                    "مكثفات تالفة للمقارنة",
                    "مصدر طاقة DC (0-25V)",
                    "مقاومة 1MΩ للتفريغ",
                    "مقاومة 1kΩ للحد من التيار",
                    "كرونومتر لقياس الزمن"
                  ],
                  procedure: [
                    "الجزء الأول: قياس السعة",
                    "تفريغ المكثف بالكامل باستخدام مقاومة التفريغ",
                    "ضبط الملتيميتر على وضع قياس السعة (Capacitance)",
                    "قياس سعة المكثفات المختلفة",
                    "مقارنة القيم المقاسة مع القيم الاسمية",
                    "حساب نسبة الخطأ والتفاوت المسموح",
                    "",
                    "الجزء الثاني: اختبار التسرب",
                    "شحن المكثف إلى جهد التشغيل المقنن",
                    "قياس تيار التسرب بعد دقيقة واحدة",
                    "تسجيل قيم التسرب للمكثفات المختلفة",
                    "",
                    "الجزء الثالث: اختبار الشحن والتفريغ",
                    "توصيل المكثف مع مقاومة في دائرة RC",
                    "قياس زمن الشحن إلى 63% من الجهد الأقصى",
                    "حساب ثابت الزمن τ = RC",
                    "مقارنة النتائج النظرية مع العملية"
                  ],
                  safetyPrecautions: [
                    "تفريغ المكثفات الكبيرة قبل التعامل معها",
                    "احترام القطبية في المكثفات الإلكتروليتية",
                    "عدم تجاوز جهد التشغيل المقنن",
                    "استخدام مقاومة التفريغ دائماً"
                  ],
                  expectedResults: {
                    capacitance: "القيم المقاسة يجب أن تكون ضمن التفاوت المسموح (±20% للإلكتروليتية)",
                    leakage: "تيار التسرب يجب أن يكون أقل من 0.01CV μA (C بالميكروفاراد، V بالفولت)",
                    timeConstant: "ثابت الزمن المقاس يجب أن يطابق المحسوب نظرياً ±10%"
                  },
                  dataSheet: {
                    headers: ["نوع المكثف", "السعة الاسمية", "السعة المقاسة", "الانحراف (%)", "تيار التسرب (μA)", "ثابت الزمن (ms)", "الحالة"],
                    sampleData: [
                      ["إلكتروليتي", "1000μF", "980μF", "-2%", "2.1", "980", "سليم"],
                      ["سيراميكي", "100nF", "98nF", "-2%", "0.01", "98", "سليم"],
                      ["فيلم", "1μF", "1.02μF", "+2%", "0.005", "1020", "سليم"],
                      ["إلكتروليتي", "470μF", "320μF", "-32%", "15.2", "320", "تالف"]
                    ]
                  }
                }
              }
            },
            {
              id: 9,
              title: "Resistors",
              titleAr: "المقاومات",
              completed: false,
              duration: "40 min",
              type: "reading",
              summary: "Current limiting components: types, color codes, series/parallel connections, and testing.",
              summaryAr: "مكونات تحديد التيار: الأنواع، رموز الألوان، التوصيلات المتسلسلة/المتوازية، والاختبار.",
              content: {
                definition: "مكون يحد من تدفق التيار في الدائرة ويحول الطاقة الكهربائية إلى حرارة",
                uses: "تحديد التيار، تقسيم الجهد، الحماية من التيار الزائد، ضبط مستوى الإشارة",
                types: {
                  fixed: "ثابتة: قيمة مقاومة ثابتة لا تتغير",
                  variable: "متغيرة (Potentiometer): قيمة قابلة للتعديل يدويًا",
                  preset: "مسبقة الضبط (Trimmer): للضبط الدقيق أثناء التصنيع"
                },
                colorCode: {
                  bands4: "4 أشرطة: الأول والثاني (الأرقام)، الثالث (المضاعف)، الرابع (التفاوت)",
                  bands5: "5 أشرطة: الأول والثاني والثالث (الأرقام)، الرابع (المضاعف)، الخامس (التفاوت)",
                  colors: {
                    black: "أسود = 0",
                    brown: "بني = 1",
                    red: "أحمر = 2",
                    orange: "برتقالي = 3",
                    yellow: "أصفر = 4",
                    green: "أخضر = 5",
                    blue: "أزرق = 6",
                    violet: "بنفسجي = 7",
                    gray: "رمادي = 8",
                    white: "أبيض = 9"
                  },
                  tolerance: {
                    gold: "ذهبي = ±5%",
                    silver: "فضي = ±10%",
                    brown: "بني = ±1%",
                    red: "أحمر = ±2%"
                  }
                },
                connections: {
                  series: "التوالي: R_total = R1 + R2 + R3 + ...",
                  parallel: "التوازي: 1/R_total = 1/R1 + 1/R2 + 1/R3 + ..."
                },
                failureMode: "دائرة مفتوحة (بسبب الحرارة الزائدة)، تغير القيمة خارج نطاق التفاوت، تلف فيزيائي",
                testing: "قياس المقاومة بالملتيميتر ومقارنتها بالقيمة المحددة، فحص بصري للتلف",
                labExperiment: {
                  title: "تجربة 9: قراءة كود الألوان واختبار المقاومات",
                  objective: "إتقان قراءة كود الألوان للمقاومات والتحقق من دقة القيم",
                  requiredEquipment: [
                    "ملتيميتر رقمي",
                    "مجموعة مقاومات مختلفة القيم (4 و 5 أشرطة)",
                    "جدول كود الألوان",
                    "عدسة مكبرة لقراءة الألوان",
                    "مقاومات تالفة للمقارنة",
                    "مصدر طاقة للاختبار تحت الحمل",
                    "أسلاك توصيل ولوحة تجارب"
                  ],
                  procedure: [
                    "الجزء الأول: قراءة كود الألوان",
                    "فحص المقاومات بصرياً وتحديد عدد الأشرطة",
                    "قراءة الألوان من اليسار إلى اليمين",
                    "تطبيق جدول كود الألوان لحساب القيمة النظرية",
                    "تحديد نسبة التفاوت المسموح من الشريط الأخير",
                    "",
                    "الجزء الثاني: القياس العملي",
                    "ضبط الملتيميتر على وضع المقاومة المناسب",
                    "قياس قيمة كل مقاومة",
                    "حساب نسبة الانحراف عن القيمة النظرية",
                    "التحقق من أن الانحراف ضمن التفاوت المسموح",
                    "",
                    "الجزء الثالث: اختبار التوصيل",
                    "توصيل مقاومتين على التوالي وقياس المقاومة الكلية",
                    "توصيل مقاومتين على التوازي وقياس المقاومة الكلية",
                    "مقارنة النتائج مع الحسابات النظرية",
                    "",
                    "الجزء الرابع: اختبار تحت الحمل",
                    "تطبيق جهد على المقاومة وقياس التيار",
                    "حساب القدرة المستهلكة P = VI",
                    "التأكد من عدم تجاوز القدرة المقننة"
                  ],
                  safetyPrecautions: [
                    "عدم تجاوز القدرة المقننة للمقاومة",
                    "فحص المقاومات بصرياً قبل الاستخدام",
                    "تجنب لمس المقاومات الساخنة",
                    "استخدام النطاق المناسب في الملتيميتر"
                  ],
                  expectedResults: {
                    colorCode: "القيم المحسوبة من كود الألوان يجب أن تطابق القيم المقاسة ضمن التفاوت",
                    series: "المقاومة الكلية للتوالي = R1 + R2",
                    parallel: "المقاومة الكلية للتوازي = (R1 × R2)/(R1 + R2)",
                    power: "القدرة المستهلكة يجب أن تكون أقل من القدرة المقننة"
                  },
                  dataSheet: {
                    headers: ["كود الألوان", "القيمة النظرية", "التفاوت", "القيمة المقاسة", "الانحراف (%)", "القدرة (mW)", "الحالة"],
                    sampleData: [
                      ["أحمر-أحمر-بني-ذهبي", "220Ω", "±5%", "218Ω", "-0.9%", "45", "سليم"],
                      ["بني-أسود-أحمر-فضي", "1kΩ", "±10%", "1.02kΩ", "+2%", "36", "سليم"],
                      ["أصفر-بنفسجي-أصفر-ذهبي", "470kΩ", "±5%", "465kΩ", "-1.1%", "0.2", "سليم"],
                      ["أخضر-أزرق-أحمر-ذهبي", "5.6kΩ", "±5%", "∞", "-", "0", "تالف"]
                    ]
                  }
                }
              }
            }
          ]
        },
        {
          id: 4,
          title: "Semiconductor Components",
          titleAr: "المكونات شبه الموصلة",
          topics: [
            {
              id: 10,
              title: "Diodes",
              titleAr: "الدايودات",
              completed: false,
              duration: "45 min",
              type: "lab",
              summary: "One-way current flow devices: types, applications, and testing methods.",
              summaryAr: "أجهزة تدفق التيار في اتجاه واحد: الأنواع، التطبيقات، وطرق الاختبار.",
              content: {
                definition: "مكون شبه موصل يسمح بمرور التيار في اتجاه واحد فقط",
                types: {
                  power: "دايودات القدرة (Power Diodes): تستخدم في دوائر التقويم",
                  signal: "دايودات الإشارة (Signal Diodes): لمعالجة الإشارات الصغيرة",
                  zener: "دايود زينر (Zener Diodes): مصمم للعمل في وضع الانحياز العكسي لتنظيم الجهد",
                  led: "الدايود الباعث للضوء (LEDs): يضيء عند مرور التيار من خلاله"
                },
                zenerUses: "منظم للجهد وحماية من الجهد الزائد",
                failureMode: "قصر في الدائرة (يسمح بمرور التيار في كلا الاتجاهين) أو دائرة مفتوحة (لا يسمح بمرور التيار)",
                testing: "باستخدام وضع اختبار الدايود في الملتيميتر، يجب أن يعطي قراءة جهد في اتجاه واحد وقراءة لا نهائية في الاتجاه المعاكس",
                labExperiment: {
                  title: "تجربة 10: اختبار الدايودات وخصائص التوصيل الأحادي",
                  objective: "فهم خصائص الدايودات واختبار التوصيل الأحادي وقياس جهد التشغيل",
                  requiredEquipment: [
                    "ملتيميتر مع وضع اختبار الدايود",
                    "مجموعة دايودات مختلفة (1N4007, 1N4148, LED, Zener)",
                    "دايودات تالفة للمقارنة",
                    "مصدر طاقة متغير (0-15V)",
                    "مقاومات حد التيار (220Ω, 1kΩ)",
                    "راسم الإشارة (اختياري لرسم المنحنى)",
                    "أسلاك توصيل ولوحة تجارب"
                  ],
                  procedure: [
                    "الجزء الأول: اختبار الاستمرارية",
                    "ضبط الملتيميتر على وضع اختبار الدايود",
                    "توصيل المجس الأحمر بالأنود والأسود بالكاثود (الانحياز الأمامي)",
                    "تسجيل قراءة جهد التشغيل (0.6-0.7V للسيليكون)",
                    "عكس التوصيل (الانحياز العكسي)",
                    "التأكد من عدم وجود توصيل (قراءة لا نهائية)",
                    "",
                    "الجزء الثاني: اختبار LED",
                    "توصيل LED مع مقاومة حد التيار 220Ω",
                    "تطبيق جهد 3-5V وملاحظة الإضاءة",
                    "قياس جهد التشغيل للـ LED (1.8-3.3V حسب اللون)",
                    "قياس التيار المار عبر الدائرة",
                    "",
                    "الجزء الثالث: اختبار دايود زينر",
                    "توصيل دايود زينر في الانحياز العكسي",
                    "زيادة الجهد تدريجياً حتى الوصول لجهد زينر",
                    "قياس جهد الانهيار وتسجيله",
                    "ملاحظة ثبات الجهد عند زيادة التيار",
                    "",
                    "الجزء الرابع: منحنى الخصائص",
                    "رسم منحنى I-V للدايود العادي",
                    "تسجيل قيم التيار والجهد في الانحياز الأمامي",
                    "ملاحظة الزيادة الأسية للتيار بعد جهد التشغيل"
                  ],
                  safetyPrecautions: [
                    "عدم تجاوز التيار الأقصى المسموح للدايود",
                    "احترام القطبية في جميع الاختبارات",
                    "استخدام مقاومة حد التيار مع LED",
                    "عدم تجاوز جهد الانهيار العكسي للدايودات العادية"
                  ],
                  expectedResults: {
                    forward: "جهد التشغيل الأمامي: 0.6-0.7V للسيليكون، 0.2-0.3V للجرمانيوم",
                    reverse: "مقاومة عكسية عالية جداً (MΩ)",
                    led: "جهد التشغيل: أحمر 1.8V، أخضر 2.2V، أزرق 3.3V",
                    zener: "جهد الانهيار ثابت ومطابق للقيمة المقننة ±5%"
                  },
                  dataSheet: {
                    headers: ["نوع الدايود", "جهد التشغيل الأمامي (V)", "المقاومة العكسية", "التيار الأمامي (mA)", "جهد زينر (V)", "الحالة"],
                    sampleData: [
                      ["1N4007", "0.65", ">10MΩ", "15", "-", "سليم"],
                      ["LED أحمر", "1.85", ">10MΩ", "12", "-", "سليم"],
                      ["LED أزرق", "3.25", ">10MΩ", "8", "-", "سليم"],
                      ["زينر 5.1V", "0.68", ">10MΩ", "10", "5.08", "سليم"],
                      ["1N4148", "0", "0Ω", "0", "-", "تالف"]
                    ]
                  }
                }
              }
            },
            {
              id: 11,
              title: "Transistors",
              titleAr: "الترانزستورات",
              completed: false,
              duration: "50 min",
              type: "interactive",
              summary: "Amplification and switching devices: operation principles and testing procedures.",
              summaryAr: "أجهزة التضخيم والتحويل: مبادئ التشغيل وإجراءات الاختبار.",
              content: {
                definition: "جهاز شبه موصل يستخدم لتضخيم الإشارات الكهربائية أو كـمفتاح إلكتروني",
                uses: "التضخيم، التحويل (Switching)",
                failureMode: "قصر أو دائرة مفتوحة بين أطرافه (القاعدة، المجمع، الباعث). يمكن أن يحدث الفشل بسبب الحرارة الزائدة أو الجهد الزائد",
                testing: "باستخدام وضع اختبار الدايود في الملتيميتر لاختبار الوصلات بين أطراف الترانزستور (B-E و B-C) كما لو كانت دايودات",
                labExperiment: {
                  title: "تجربة 11: اختبار الترانزستورات وقياس خصائص التضخيم والتحويل",
                  objective: "فهم مبدأ عمل الترانزستور واختبار خصائص التضخيم والتحويل وقياس معامل التضخيم",
                  requiredEquipment: [
                    "ملتيميتر مع وضع اختبار الدايود",
                    "ترانزستورات مختلفة (2N2222, BC547, 2N3904)",
                    "ترانزستورات تالفة للمقارنة",
                    "مصدر طاقة ثنائي (±12V)",
                    "مولد إشارة للاختبار",
                    "راسم الإشارة لقياس التضخيم",
                    "مقاومات مختلفة (1kΩ, 10kΩ, 100kΩ)",
                    "مكثفات اقتران (10μF, 100μF)",
                    "LED للاختبار كمفتاح"
                  ],
                  procedure: [
                    "الجزء الأول: تحديد أطراف الترانزستور",
                    "استخدام ملتيميتر على وضع اختبار الدايود",
                    "اختبار جميع التوصيلات الممكنة بين الأطراف",
                    "تحديد القاعدة (Base): الطرف المشترك للوصلتين",
                    "تحديد المجمع (Collector) والباعث (Emitter)",
                    "التأكد من نوع الترانزستور (NPN أو PNP)",
                    "",
                    "الجزء الثاني: اختبار الوصلات",
                    "اختبار وصلة Base-Emitter (يجب أن تعمل كدايود)",
                    "اختبار وصلة Base-Collector (يجب أن تعمل كدايود)",
                    "اختبار وصلة Collector-Emitter (يجب ألا توصل بدون إشارة القاعدة)",
                    "تسجيل جهود التشغيل للوصلات",
                    "",
                    "الجزء الثالث: قياس معامل التضخيم (hFE)",
                    "بناء دائرة اختبار بسيطة مع مقاومة قاعدة 10kΩ",
                    "تطبيق جهد قاعدة متغير (0-5V)",
                    "قياس تيار القاعدة IB وتيار المجمع IC",
                    "حساب معامل التضخيم β = IC/IB",
                    "رسم منحنى IC مقابل IB",
                    "",
                    "الجزء الرابع: اختبار كمفتاح إلكتروني",
                    "توصيل LED مع مقاومة في دائرة المجمع",
                    "تطبيق إشارة رقمية على القاعدة (0V/5V)",
                    "ملاحظة تشغيل وإطفاء LED",
                    "قياس جهود التشبع والقطع",
                    "",
                    "الجزء الخامس: دائرة تضخيم بسيطة",
                    "بناء مضخم باعث مشترك",
                    "تطبيق إشارة جيبية صغيرة على الدخل",
                    "قياس إشارة الخرج بالراسم",
                    "حساب معامل التضخيم للجهد Av = Vout/Vin"
                  ],
                  safetyPrecautions: [
                    "عدم تجاوز الحد الأقصى لتيار المجمع",
                    "تجنب تطبيق جهود عكسية عالية",
                    "استخدام مقاومات حد التيار",
                    "مراقبة درجة حرارة الترانزستور أثناء التشغيل"
                  ],
                  expectedResults: {
                    junctionTest: "وصلة B-E: 0.6-0.7V، وصلة B-C: 0.6-0.7V",
                    gainFactor: "معامل التضخيم β: 50-300 للترانزستورات الصغيرة",
                    saturationVoltage: "جهد التشبع VCE(sat): 0.1-0.3V",
                    cutoffCurrent: "تيار القطع: أقل من 1μA",
                    amplification: "تضخيم الجهد: 10-100 مرة حسب التصميم"
                  },
                  dataSheet: {
                    headers: ["نوع الترانزستور", "جهد B-E (V)", "جهد B-C (V)", "معامل التضخيم β", "VCE(sat) (V)", "الحالة"],
                    sampleData: [
                      ["2N2222 (NPN)", "0.65", "0.68", "180", "0.2", "سليم"],
                      ["BC547 (NPN)", "0.62", "0.64", "220", "0.15", "سليم"],
                      ["2N3906 (PNP)", "0.68", "0.70", "150", "0.25", "سليم"],
                      ["مجهول", "0", "∞", "0", "12V", "تالف"]
                    ]
                  },
                  practicalResults: {
                    title: "التطبيقات العملية والنتائج",
                    amplifierApplications: [
                      "مضخمات الصوت في الأجهزة الطبية",
                      "مضخمات الإشارات الحيوية (ECG, EEG)",
                      "مضخمات التيار في دوائر الاستشعار",
                      "مضخمات العزل في الأجهزة الحساسة"
                    ],
                    switchingApplications: [
                      "مفاتيح إلكترونية في دوائر التحكم",
                      "مشغلات LED في شاشات العرض",
                      "مفاتيح القدرة في محولات SMPS",
                      "دوائر المنطق الرقمي"
                    ],
                    designConsiderations: [
                      "اختيار نقطة التشغيل المناسبة",
                      "حساب مقاومات التحيز",
                      "تصميم دوائر الحماية من الحرارة",
                      "اعتبارات الضوضاء في التضخيم"
                    ]
                  }
                }
              }
            }
          ]
        },
        {
          id: 5,
          title: "Integrated Circuits & Timing",
          titleAr: "الدوائر المتكاملة والتوقيت",
          topics: [
            {
              id: 12,
              title: "Integrated Circuits (ICs)",
              titleAr: "الدوائر المتكاملة",
              completed: false,
              duration: "40 min",
              type: "reading",
              summary: "Complete circuits on silicon chips: pin arrangements and testing methods.",
              summaryAr: "دوائر كاملة على رقائق السيليكون: ترتيب الأطراف وطرق الاختبار.",
              content: {
                definition: "دائرة إلكترونية كاملة مدمجة على شريحة صغيرة من السيليكون",
                pinArrangement: "كيفية تحديد الطرف رقم 1 (غالبًا بوجود نقطة أو شق)",
                testingTips: [
                  "الفحص البصري بحثًا عن شقوق أو علامات احتراق",
                  "التحقق من وجود جهد التشغيل (VCC) والأرضي (GND) باستخدام الملتيميتر",
                  "ملاحظة ارتفاع درجة حرارة الدائرة بشكل غير طبيعي",
                  "الاختبار المتقدم يتطلب معدات متخصصة ومقارنة الإشارات مع ورقة البيانات (Datasheet)"
                ],
                labExperiment: {
                  title: "تجربة 12: اختبار الدوائر المتكاملة وتحليل الوظائف الأساسية",
                  objective: "تعلم طرق اختبار الدوائر المتكاملة وفهم وظائفها وتحديد الأعطال الشائعة",
                  requiredEquipment: [
                    "ملتيميتر رقمي عالي الدقة",
                    "راسم الإشارة متعدد القنوات",
                    "مولد إشارة رقمي",
                    "مصدر طاقة مستقر (±5V, ±12V)",
                    "دوائر متكاملة مختلفة (LM358, 555, 74HC00)",
                    "دوائر متكاملة تالفة للمقارنة",
                    "لوحة تجارب ومكونات مساعدة",
                    "مقياس حرارة بالأشعة تحت الحمراء",
                    "عدسة مكبرة للفحص البصري"
                  ],
                  procedure: [
                    "الجزء الأول: الفحص البصري والتحضير",
                    "فحص الدائرة المتكاملة بصرياً بحثاً عن تلف فيزيائي",
                    "تحديد الطرف رقم 1 باستخدام النقطة أو الشق",
                    "قراءة رقم الدائرة المتكاملة والبحث عن ورقة البيانات",
                    "تحديد أطراف التغذية (VCC, VDD, GND, VSS)",
                    "",
                    "الجزء الثاني: اختبار التغذية والاستهلاك",
                    "توصيل جهد التغذية المناسب للدائرة المتكاملة",
                    "قياس تيار الاستهلاك في حالة الخمول",
                    "مقارنة التيار المقاس مع المواصفات",
                    "مراقبة درجة حرارة الدائرة أثناء التشغيل",
                    "",
                    "الجزء الثالث: اختبار الوظائف الأساسية",
                    "للمضخم التشغيلي (LM358):",
                    "اختبار وضع المتابع (Buffer)",
                    "قياس معامل التضخيم في الحلقة المفتوحة",
                    "اختبار الاستجابة الترددية",
                    "",
                    "للمؤقت 555:",
                    "بناء دائرة مذبذب غير مستقر",
                    "قياس تردد التذبذب ودورة العمل",
                    "اختبار وضع المؤقت أحادي الاستقرار",
                    "",
                    "للبوابة المنطقية (74HC00):",
                    "اختبار جدول الحقيقة لبوابة NAND",
                    "قياس مستويات الجهد المنطقية",
                    "اختبار زمن التأخير والانتشار",
                    "",
                    "الجزء الرابع: اختبار الحماية والحدود",
                    "اختبار حماية الدخل من الجهد الزائد",
                    "قياس الحد الأقصى لتيار الخرج",
                    "اختبار الاستجابة لدرجات الحرارة المختلفة"
                  ],
                  safetyPrecautions: [
                    "عدم تجاوز جهد التغذية الأقصى المسموح",
                    "تجنب التوصيل العكسي للتغذية",
                    "استخدام حماية من التفريغ الكهروستاتيكي (ESD)",
                    "عدم لمس أطراف الدائرة المتكاملة مباشرة",
                    "مراقبة درجة الحرارة لتجنب التلف الحراري"
                  ],
                  expectedResults: {
                    powerConsumption: "تيار الاستهلاك ضمن المواصفات (عادة 1-10mA)",
                    functionality: "جميع الوظائف تعمل حسب جدول الحقيقة أو المواصفات",
                    outputLevels: "مستويات الخرج ضمن النطاقات المحددة",
                    frequency: "الاستجابة الترددية ضمن النطاق المحدد",
                    temperature: "درجة حرارة التشغيل أقل من 70°C"
                  },
                  dataSheet: {
                    headers: ["نوع الدائرة", "جهد التغذية (V)", "تيار الاستهلاك (mA)", "درجة الحرارة (°C)", "الوظيفة", "الحالة"],
                    sampleData: [
                      ["LM358", "5.0", "3.2", "35", "مضخم تشغيلي", "سليم"],
                      ["NE555", "5.0", "6.8", "42", "مؤقت", "سليم"],
                      ["74HC00", "5.0", "2.1", "28", "بوابة NAND", "سليم"],
                      ["LM358", "5.0", "25.6", "85", "لا يعمل", "تالف"]
                    ]
                  },
                  practicalResults: {
                    title: "التطبيقات العملية والنتائج",
                    amplifierTests: [
                      "اختبار مضخم الإشارات الحيوية في الأجهزة الطبية",
                      "قياس دقة التضخيم في أجهزة القياس",
                      "اختبار الضوضاء في المضخمات الحساسة",
                      "تقييم الاستقرار الحراري للمضخمات"
                    ],
                    timerApplications: [
                      "مولدات النبضات في أجهزة العلاج الطبيعي",
                      "مؤقتات التحكم في أجهزة التعقيم",
                      "مذبذبات الساعة في الأجهزة الرقمية",
                      "دوائر التأخير في أنظمة الحماية"
                    ],
                    digitalLogic: [
                      "اختبار البوابات المنطقية في المعالجات",
                      "تحليل الإشارات الرقمية في أجهزة التحكم",
                      "قياس أزمنة التأخير في الدوائر السريعة",
                      "اختبار مقاومة الضوضاء في البيئات الطبية"
                    ]
                  }
                }
              }
            },
            {
              id: 13,
              title: "Crystals and Resonators",
              titleAr: "البلورات والمرينات",
              completed: false,
              duration: "35 min",
              type: "lab",
              summary: "Precise frequency generation: crystal oscillators vs ceramic resonators.",
              summaryAr: "توليد التردد الدقيق: المذبذبات البلورية مقابل المرنانات السيراميكية.",
              content: {
                definition: "مكونات تستخدم لتوليد إشارة تردد دقيقة ومستقرة",
                types: {
                  crystal: "مذبذب بلوري (Crystal Oscillator): يستخدم بلورة كوارتز، وهو دقيق جدًا ومستقر",
                  ceramic: "مرنان سيراميكي (Ceramic Resonator): يستخدم مادة سيراميكية، وهو أقل تكلفة ودقة من البلوري"
                },
                uses: "توفير إشارة الساعة (Clock Signal) للمعالجات الدقيقة والدوائر الرقمية",
                testing: "يتطلب عادةً راسم إشارة (Oscilloscope) للتحقق من وجود التذبذب الصحيح على أطرافه"
              }
            },
            {
              id: 14,
              title: "Voltage Regulators",
              titleAr: "منظمات الجهد",
              completed: false,
              duration: "40 min",
              type: "interactive",
              summary: "Stable voltage output devices: linear regulators and testing procedures.",
              summaryAr: "أجهزة خرج الجهد المستقر: المنظمات الخطية وإجراءات الاختبار.",
              content: {
                definition: "دائرة متكاملة توفر جهد خرج ثابت بغض النظر عن التغيرات في جهد الدخل أو الحمل",
                types: "سلسلة موجبة (مثل 78xx) وسلسلة سالبة (مثل 79xx)",
                uses: "توفير جهد مستقر وموثوق للدوائر الإلكترونية",
                testing: "قياس جهد الدخل وجهد الخرج باستخدام الملتيميتر. يجب أن يكون جهد الخرج ثابتًا وقريبًا جدًا من القيمة المحددة للمنظم (مثلاً، 5 فولت لمنظم 7805)"
              }
            }
          ]
        },
        {
          id: 6,
          title: "Power Systems & Conclusion",
          titleAr: "أنظمة الطاقة والخاتمة",
          topics: [
            {
              id: 15,
              title: "Switch Mode Power Supply (SMPS)",
              titleAr: "مزود الطاقة بوضع التبديل",
              completed: false,
              duration: "55 min",
              type: "video",
              summary: "High-efficiency power conversion: principles, advantages, and applications.",
              summaryAr: "تحويل الطاقة عالي الكفاءة: المبادئ، المزايا، والتطبيقات.",
              content: {
                definition: "نوع من مزودات الطاقة يستخدم منظمًا تبديليًا لتحويل الطاقة الكهربائية بكفاءة عالية (85-95%)",
                operation: {
                  step1: "تحويل التيار المتردد إلى تيار مستمر عالي الجهد",
                  step2: "تقطيع الجهد المستمر بتردد عالٍ (20-100 kHz) باستخدام ترانزستورات القدرة",
                  step3: "تمرير الإشارة المقطعة عبر محول عالي التردد لتغيير مستوى الجهد",
                  step4: "تقويم وترشيح الخرج للحصول على جهد مستمر مستقر",
                  step5: "دائرة التحكم والتغذية الراجعة لضبط الجهد"
                },
                components: {
                  rectifier: "مقوم الدخل: تحويل AC إلى DC",
                  filter: "مرشح الدخل: تنعيم الجهد المقوم",
                  switcher: "دائرة التبديل: ترانزستورات MOSFET أو IGBT",
                  transformer: "محول عالي التردد: عزل وتغيير مستوى الجهد",
                  outputRectifier: "مقوم الخرج: تقويم الجهد المنخفض",
                  outputFilter: "مرشح الخرج: تنعيم جهد الخرج",
                  controller: "دائرة التحكم: PWM وحلقة التغذية الراجعة"
                },
                advantages: [
                  "كفاءة عالية (85-95%) مقارنة بالمنظمات الخطية (60-70%)",
                  "حجم ووزن أصغر بكثير",
                  "توليد حرارة أقل",
                  "مدى واسع من جهد الدخل",
                  "تنظيم ممتاز للجهد"
                ],
                disadvantages: [
                  "دائرة أكثر تعقيدًا",
                  "توليد ضوضاء كهربائية (EMI)",
                  "تكلفة أعلى للمكونات",
                  "صعوبة في الإصلاح",
                  "حساسية للأحمال الخفيفة"
                ],
                applications: [
                  "أجهزة الكمبيوتر والخوادم",
                  "شواحن الهواتف المحمولة",
                  "أجهزة التلفزيون والشاشات",
                  "المعدات الطبية",
                  "أنظمة الإضاءة LED"
                ],
                labExperiment: {
                  title: "تجربة 15: تحليل وقياس خصائص مزود الطاقة التبديلي",
                  objective: "فهم مبدأ عمل SMPS وقياس الكفاءة والتنظيم وتحليل الضوضاء",
                  requiredEquipment: [
                    "مزود طاقة تبديلي قابل للفتح (5V/2A)",
                    "ملتيميتر رقمي عالي الدقة",
                    "راسم الإشارة (Oscilloscope)",
                    "مقياس الطاقة (Power Meter)",
                    "حمل إلكتروني متغير (Electronic Load)",
                    "مقاومات حمل مختلفة (10Ω, 25Ω, 50Ω)",
                    "مكثف ترشيح إضافي",
                    "مجس تيار (Current Probe)"
                  ],
                  procedure: [
                    "الجزء الأول: قياس الكفاءة",
                    "توصيل مقياس الطاقة عند الدخل لقياس الطاقة المستهلكة",
                    "توصيل الحمل المتغير عند الخرج",
                    "قياس جهد وتيار الخرج عند أحمال مختلفة (25%, 50%, 75%, 100%)",
                    "حساب الكفاءة = (طاقة الخرج / طاقة الدخل) × 100%",
                    "رسم منحنى الكفاءة مقابل الحمل",
                    "",
                    "الجزء الثاني: قياس التنظيم",
                    "قياس جهد الخرج بدون حمل (No Load)",
                    "تطبيق الحمل الكامل وقياس جهد الخرج",
                    "حساب تنظيم الحمل = ((V_no_load - V_full_load) / V_full_load) × 100%",
                    "تغيير جهد الدخل من 90V إلى 264V وقياس تأثيره على الخرج",
                    "",
                    "الجزء الثالث: تحليل الضوضاء والتموج",
                    "توصيل راسم الإشارة عند خرج SMPS",
                    "ضبط راسم الإشارة على AC Coupling",
                    "قياس تموج الجهد (Voltage Ripple) عند أحمال مختلفة",
                    "قياس تردد التبديل باستخدام مجس التيار",
                    "تحليل طيف الترددات للضوضاء الكهرومغناطيسية",
                    "",
                    "الجزء الرابع: اختبار الحماية",
                    "اختبار حماية التيار الزائد بزيادة الحمل تدريجياً",
                    "اختبار حماية الجهد الزائد عند الدخل",
                    "اختبار حماية درجة الحرارة (إن وجدت)",
                    "قياس زمن الاستجابة للحماية"
                  ],
                  safetyPrecautions: [
                    "فصل الطاقة قبل فتح مزود الطاقة",
                    "تفريغ المكثفات الكبيرة قبل اللمس",
                    "عدم لمس الدوائر أثناء التشغيل",
                    "استخدام عازل كهربائي عند القياس",
                    "مراقبة درجة الحرارة أثناء الاختبار"
                  ],
                  expectedResults: {
                    efficiency: "كفاءة 85-95% عند الحمل الكامل",
                    regulation: "تنظيم الحمل أقل من 1%، تنظيم الخط أقل من 0.5%",
                    ripple: "تموج الخرج أقل من 50mV peak-to-peak",
                    frequency: "تردد التبديل 20-100 kHz",
                    protection: "تفعيل الحماية عند 110-120% من الحمل المقنن"
                  },
                  dataSheet: {
                    headers: ["الحمل (%)", "جهد الدخل (V)", "تيار الدخل (A)", "جهد الخرج (V)", "تيار الخرج (A)", "الكفاءة (%)", "التموج (mV)"],
                    sampleData: [
                      ["25%", "230", "0.15", "5.02", "0.50", "87%", "25"],
                      ["50%", "230", "0.28", "5.01", "1.00", "91%", "35"],
                      ["75%", "230", "0.41", "5.00", "1.50", "93%", "42"],
                      ["100%", "230", "0.54", "4.99", "2.00", "92%", "48"]
                    ]
                  }
                }
              }
            },
            {
              id: 16,
              title: "Course Conclusion",
              titleAr: "خاتمة الدورة",
              completed: false,
              duration: "30 min",
              type: "reading",
              summary: "Review of concepts, safety reminders, and practical application encouragement.",
              summaryAr: "مراجعة المفاهيم، تذكيرات السلامة، وتشجيع التطبيق العملي.",
              content: {
                review: {
                  fundamentals: "المفاهيم الأساسية: التيار، الجهد، المقاومة، قانون أوم",
                  tools: "الأدوات الأساسية: الملتيميتر، مكواة اللحام، راسم الإشارة",
                  protection: "مكونات الحماية: الفيوزات، MOV، الثرمستورات",
                  passive: "المكونات السلبية: المحاثات، المرحلات، المكثفات، المقاومات",
                  semiconductor: "أشباه الموصلات: الدايودات، الترانزستورات",
                  integrated: "الدوائر المتكاملة: ICs، البلورات، منظمات الجهد",
                  power: "أنظمة الطاقة: SMPS ومبادئ تحويل الطاقة"
                },
                safety: {
                  electrical: "فصل الطاقة دائمًا قبل بدء العمل",
                  tools: "استخدام الأدوات المناسبة والمعايرة",
                  esd: "الحماية من التفريغ الكهروستاتيكي (ESD)",
                  ventilation: "ضمان التهوية الجيدة عند اللحام",
                  documentation: "قراءة أوراق البيانات قبل التعامل مع المكونات"
                },
                practicalProjects: [
                  "بناء دائرة LED بسيطة مع مقاومة",
                  "اختبار مكونات مختلفة بالملتيميتر",
                  "إصلاح جهاز إلكتروني بسيط",
                  "بناء مزود طاقة منظم",
                  "تصميم دائرة إنذار بسيطة"
                ],
                nextSteps: [
                  "دراسة الدوائر الرقمية والمعالجات الدقيقة",
                  "تعلم تصميم الدوائر المطبوعة (PCB)",
                  "التخصص في مجال معين (طاقة، اتصالات، تحكم)",
                  "الحصول على شهادات مهنية في الإلكترونيات",
                  "المشاركة في مشاريع مفتوحة المصدر"
                ],
                encouragement: "تطبيق المعرفة النظرية عمليًا هو أفضل طريقة للتعلم. ابدأ بمشاريع بسيطة وتدرج نحو الأكثر تعقيدًا"
              }
            }
          ]
        }
      ]
    }
  ];

  const userProgress = {
    totalCourses: 6,
    completedCourses: 2,
    totalHours: 165,
    currentStreak: 7,
    level: "Intermediate"
  };

  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white">
        <h1 className="text-4xl font-bold mb-2">Welcome back, Alex!</h1>
        <p className="text-blue-100 text-lg">Continue your medical engineering journey</p>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Courses</p>
              <p className="text-2xl font-bold text-gray-900">{userProgress.totalCourses}</p>
            </div>
            <BookOpen className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{userProgress.completedCourses}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Study Hours</p>
              <p className="text-2xl font-bold text-purple-600">{userProgress.totalHours}</p>
            </div>
            <Clock className="h-8 w-8 text-purple-500" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Current Streak</p>
              <p className="text-2xl font-bold text-orange-600">{userProgress.currentStreak} days</p>
            </div>
            <Star className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Current Courses */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Courses</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {courses.map((course) => (
            <div key={course.id} className={`bg-white rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300 hover:border-blue-300 ${course.id === 4 ? 'ring-2 ring-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50' : ''}`}>
              {course.id === 4 && (
                <div className="bg-gradient-to-r from-yellow-500 to-orange-500 p-3 rounded-t-xl">
                  <div className="flex items-center justify-center gap-2 text-white">
                    <Zap className="h-5 w-5" />
                    <span className="font-medium">Essential Electronics Lab</span>
                    <Zap className="h-5 w-5" />
                  </div>
                </div>
              )}
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      {course.id === 4 && <Cpu className="h-5 w-5 text-orange-500" />}
                      <h3 className="text-lg font-semibold text-gray-900">
                        {course.titleAr ? course.titleAr : course.title}
                      </h3>
                    </div>
                    {course.titleAr && (
                      <h4 className="text-sm font-medium text-gray-600 mb-2">{course.title}</h4>
                    )}
                    <p className="text-sm text-gray-600 mb-3">
                      {course.descriptionAr ? course.descriptionAr : course.description}
                    </p>
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                      <span className="flex items-center gap-1">
                        <GraduationCap className="h-4 w-4" />
                        {course.level}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {course.duration}
                      </span>
                      <span className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        {course.rating}
                      </span>
                    </div>
                    {course.id === 4 && (
                      <div className="space-y-2 mb-3">
                        <div className="flex items-center gap-2 text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded-full w-fit">
                          <Radio className="h-3 w-3" />
                          <span>16 Electronics Lessons in Arabic</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-blue-600 bg-blue-100 px-2 py-1 rounded-full w-fit">
                          <Cpu className="h-3 w-3" />
                          <span>Comprehensive Technical Content</span>
                        </div>
                        <div className="flex items-center gap-2 text-xs text-green-600 bg-green-100 px-2 py-1 rounded-full w-fit">
                          <Wrench className="h-3 w-3" />
                          <span>Practical Testing Procedures</span>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Progress</span>
                    <span className="text-sm font-medium text-blue-600">{course.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${course.progress}%` }}
                    ></div>
                  </div>
                </div>
                
                <button 
                  onClick={() => { setSelectedCourse(course); setActiveTab('course'); }}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Continue Learning
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Completed "Introduction to Biomedical Engineering"</p>
              <p className="text-xs text-gray-500">2 hours ago</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
            <Play className="h-5 w-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Started "Classification of Medical Devices"</p>
              <p className="text-xs text-gray-500">1 day ago</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
            <MessageSquare className="h-5 w-5 text-purple-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Posted in "Medical Devices" discussion</p>
              <p className="text-xs text-gray-500">3 days ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCourse = () => {
    const isArabicCourse = selectedCourse?.titleAr;
    const isElectronicsLab = selectedCourse?.id === 4;

    return (
      <div className="space-y-6">
        {/* Course Header */}
        <div className={`rounded-2xl p-8 text-white ${isElectronicsLab ? 'bg-gradient-to-r from-yellow-600 to-orange-600' : 'bg-gradient-to-r from-blue-600 to-teal-600'}`}>
          <button
            onClick={() => setActiveTab('dashboard')}
            className="text-blue-100 hover:text-white mb-4 flex items-center gap-2"
          >
            ← {isArabicCourse ? 'العودة للوحة التحكم' : 'Back to Dashboard'}
          </button>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <h1 className="text-3xl font-bold mb-2">
                {isArabicCourse ? selectedCourse?.titleAr : selectedCourse?.title}
              </h1>
              {isArabicCourse && (
                <h2 className="text-xl text-blue-100 mb-2">{selectedCourse?.title}</h2>
              )}
              <p className="text-blue-100 mb-4">
                {isArabicCourse ? selectedCourse?.descriptionAr : selectedCourse?.description}
              </p>
              <div className="flex items-center gap-6 text-sm">
                <span>{isArabicCourse ? 'المدرس:' : 'Instructor:'} {selectedCourse?.instructor}</span>
                <span>{isArabicCourse ? 'المدة:' : 'Duration:'} {selectedCourse?.duration}</span>
                <span>{isArabicCourse ? 'المستوى:' : 'Level:'} {selectedCourse?.level}</span>
              </div>
            </div>
            {isElectronicsLab && (
              <div className="flex items-center gap-3">
                <Zap className="h-12 w-12 text-yellow-200" />
                <div className="text-right">
                  <p className="text-lg font-bold">Essential Lab</p>
                  <p className="text-sm text-yellow-200">Electronics Course</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Course Progress */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-bold text-gray-900">
              {isArabicCourse ? 'تقدم الدورة' : 'Course Progress'}
            </h2>
            <span className="text-sm font-medium text-blue-600">
              {selectedCourse?.progress}% {isArabicCourse ? 'مكتمل' : 'Complete'}
            </span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-3">
            <div
              className={`h-3 rounded-full transition-all duration-300 ${isElectronicsLab ? 'bg-gradient-to-r from-yellow-500 to-orange-500' : 'bg-blue-600'}`}
              style={{ width: `${selectedCourse?.progress}%` }}
            ></div>
          </div>
        </div>

        {/* Course Modules */}
        <div className="space-y-6">
          {selectedCourse?.modules.map((module) => (
            <div key={module.id} className={`bg-white rounded-xl border border-gray-200 ${isElectronicsLab ? 'border-orange-200' : ''}`}>
              <div className={`p-6 border-b border-gray-200 ${isElectronicsLab ? 'bg-gradient-to-r from-orange-50 to-yellow-50' : ''}`}>
                <div className="flex items-center gap-3">
                  {isElectronicsLab && (
                    <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{module.id}</span>
                    </div>
                  )}
                  <h3 className="text-lg font-semibold text-gray-900">
                    {isArabicCourse ? `الوحدة ${module.id}: ${module.titleAr || module.title}` : `Module ${module.id}: ${module.title}`}
                  </h3>
                </div>
              </div>
              <div className="p-6">
                <div className="space-y-3">
                  {module.topics.map((topic) => (
                    <div key={topic.id} className={`flex items-center justify-between p-4 rounded-lg hover:bg-gray-100 transition-colors ${isElectronicsLab ? 'bg-orange-50 hover:bg-orange-100' : 'bg-gray-50'}`}>
                      <div className="flex items-center gap-3">
                        <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                          topic.completed ? 'bg-green-500' : (isElectronicsLab ? 'bg-orange-300' : 'bg-gray-300')
                        }`}>
                          {topic.completed && <CheckCircle className="h-4 w-4 text-white" />}
                        </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="font-medium text-gray-900">
                                {isArabicCourse ? (topic.titleAr || topic.title) : topic.title}
                              </h4>
                              {topic.content?.labExperiment && (
                                <span className="bg-orange-100 text-orange-600 text-xs px-2 py-1 rounded-full flex items-center gap-1">
                                  <Beaker className="h-3 w-3" />
                                  تجربة
                                </span>
                              )}
                            </div>
                            {isArabicCourse && topic.titleAr && (
                              <p className="text-xs text-gray-600 mb-1">{topic.title}</p>
                            )}
                            <p className="text-sm text-gray-500">{topic.duration} • {topic.type}</p>
                            {topic.content?.labExperiment && (
                              <p className="text-xs text-orange-600 mt-1">
                                تتضمن تجربة معملية شاملة مع جدول البيانات
                              </p>
                            )}
                          </div>
                        </div>
                        <button
                          onClick={() => { setSelectedTopic(topic); setActiveTab('topic'); }}
                          className={`font-medium ${isElectronicsLab ? 'text-orange-600 hover:text-orange-700' : 'text-blue-600 hover:text-blue-700'}`}
                        >
                          {isArabicCourse
                            ? (topic.completed ? 'مراجعة' : 'ابدأ')
                            : (topic.completed ? 'Review' : 'Start')
                          }
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      );
    };

  const renderTopic = () => {
    const isArabicContent = selectedTopic?.titleAr;
    const isElectronicsLab = selectedCourse?.id === 4;

    return (
      <div className="space-y-6">
        {/* Topic Header */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <button
            onClick={() => setActiveTab('course')}
            className="text-blue-600 hover:text-blue-700 mb-4 flex items-center gap-2"
          >
            ← {isArabicContent ? 'العودة للدورة' : 'Back to Course'}
          </button>
          <div className="flex flex-col md:flex-row md:items-start md:justify-between gap-4">
            <div className="flex-1">
              <h1 className="text-2xl font-bold text-gray-900 mb-2">
                {isArabicContent ? selectedTopic?.titleAr : selectedTopic?.title}
              </h1>
              {isArabicContent && (
                <h2 className="text-lg text-gray-600 mb-2">{selectedTopic?.title}</h2>
              )}
              <p className="text-gray-600 mb-4">
                {isArabicContent ? selectedTopic?.summaryAr : selectedTopic?.summary}
              </p>
              <div className="flex items-center gap-4 text-sm text-gray-500">
                <span className="flex items-center gap-1">
                  <Clock className="h-4 w-4" />
                  {selectedTopic?.duration}
                </span>
                <span className="flex items-center gap-1 capitalize">
                  <BookOpen className="h-4 w-4" />
                  {selectedTopic?.type}
                </span>
              </div>
            </div>
            {isElectronicsLab && (
              <div className="flex items-center gap-2">
                <Zap className="h-8 w-8 text-yellow-500" />
                <div className="text-right">
                  <p className="text-sm font-medium text-gray-700">Electronics Lab</p>
                  <p className="text-xs text-gray-500">Essential Components</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Topic Content */}
        <div className="bg-white rounded-xl border border-gray-200 p-8">
          <div className={`prose max-w-none ${isArabicContent ? 'text-right' : ''}`} dir={isArabicContent ? 'rtl' : 'ltr'}>
            <h2 className="text-xl font-bold text-gray-900 mb-6">
              {isArabicContent ? 'محتوى الدرس' : 'Content Overview'}
            </h2>

            {selectedTopic?.type === 'video' && (
              <div className="mb-8">
                <div className="bg-gray-900 rounded-lg aspect-video flex items-center justify-center">
                  <div className="text-center text-white">
                    <Play className="h-16 w-16 mx-auto mb-4 opacity-70" />
                    <p>{isArabicContent ? 'سيتم عرض محتوى الفيديو هنا' : 'Video content would be embedded here'}</p>
                  </div>
                </div>
              </div>
            )}

            {/* Electronics Lab Specific Content */}
            {isElectronicsLab && selectedTopic?.content && (
              <div className="space-y-6">
                {/* Basic Concepts */}
                {selectedTopic.content.concepts && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-yellow-500" />
                      المفاهيم الأساسية
                    </h3>
                    <ul className="space-y-3 text-gray-700">
                      {selectedTopic.content.concepts.map((concept, index) => (
                        <li key={index} className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                          <span className="w-2 h-2 bg-blue-500 rounded-full mt-2 flex-shrink-0"></span>
                          <span className="leading-relaxed">{concept}</span>
                        </li>
                      ))}
                    </ul>
                  </section>
                )}

                {/* Tools */}
                {selectedTopic.content.tools && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Wrench className="h-5 w-5 text-gray-600" />
                      الأدوات الأساسية
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {selectedTopic.content.tools.map((tool, index) => (
                        <div key={index} className="p-4 bg-gray-50 rounded-lg border border-gray-200">
                          <p className="text-gray-700 leading-relaxed">{tool}</p>
                        </div>
                      ))}
                    </div>
                  </section>
                )}

                {/* Component Details */}
                {(selectedTopic.content.definition || selectedTopic.content.purpose) && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Cpu className="h-5 w-5 text-blue-500" />
                      تفاصيل المكون
                    </h3>
                    <div className="space-y-4">
                      {selectedTopic.content.definition && (
                        <div className="p-4 bg-green-50 rounded-lg border border-green-200">
                          <h4 className="font-semibold text-green-800 mb-2">التعريف:</h4>
                          <p className="text-green-700">{selectedTopic.content.definition}</p>
                        </div>
                      )}
                      {selectedTopic.content.purpose && (
                        <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <h4 className="font-semibold text-blue-800 mb-2">الغرض:</h4>
                          <p className="text-blue-700">{selectedTopic.content.purpose}</p>
                        </div>
                      )}
                      {selectedTopic.content.failureMode && (
                        <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                          <h4 className="font-semibold text-red-800 mb-2">وضع الفشل الشائع:</h4>
                          <p className="text-red-700">{selectedTopic.content.failureMode}</p>
                        </div>
                      )}
                      {selectedTopic.content.testing && (
                        <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200">
                          <h4 className="font-semibold text-yellow-800 mb-2">الاختبار:</h4>
                          <p className="text-yellow-700">{selectedTopic.content.testing}</p>
                        </div>
                      )}
                    </div>
                  </section>
                )}

                {/* Types */}
                {selectedTopic.content.types && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Settings className="h-5 w-5 text-purple-500" />
                      الأنواع
                    </h3>
                    <div className="space-y-3">
                      {typeof selectedTopic.content.types === 'string' ? (
                        <p className="text-gray-700 p-3 bg-purple-50 rounded-lg">{selectedTopic.content.types}</p>
                      ) : (
                        Object.entries(selectedTopic.content.types).map(([key, value]) => (
                          <div key={key} className="p-3 bg-purple-50 rounded-lg border border-purple-200">
                            <p className="text-purple-700">{value}</p>
                          </div>
                        ))
                      )}
                    </div>
                  </section>
                )}

                {/* Color Code for Resistors */}
                {selectedTopic.content.colorCode && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Gauge className="h-5 w-5 text-indigo-500" />
                      كود الألوان
                    </h3>
                    <div className="space-y-4">
                      {selectedTopic.content.colorCode.bands4 && (
                        <div className="p-4 bg-indigo-50 rounded-lg">
                          <h4 className="font-semibold text-indigo-800 mb-2">4 أشرطة:</h4>
                          <p className="text-indigo-700">{selectedTopic.content.colorCode.bands4}</p>
                        </div>
                      )}
                      {selectedTopic.content.colorCode.colors && (
                        <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                          {Object.entries(selectedTopic.content.colorCode.colors).map(([key, value]) => (
                            <div key={key} className="p-2 bg-gray-100 rounded text-center text-sm">
                              {value}
                            </div>
                          ))}
                        </div>
                      )}
                    </div>
                  </section>
                )}

                {/* Safety Information */}
                {selectedTopic.content.safety && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Shield className="h-5 w-5 text-red-500" />
                      معلومات السلامة
                    </h3>
                    <div className="space-y-3">
                      {typeof selectedTopic.content.safety === 'string' ? (
                        <div className="p-4 bg-red-50 rounded-lg border border-red-200">
                          <p className="text-red-700">{selectedTopic.content.safety}</p>
                        </div>
                      ) : (
                        Object.entries(selectedTopic.content.safety).map(([key, value]) => (
                          <div key={key} className="p-3 bg-red-50 rounded-lg border border-red-200">
                            <p className="text-red-700">{value}</p>
                          </div>
                        ))
                      )}
                    </div>
                  </section>
                )}

                {/* SMPS Components */}
                {selectedTopic.content.components && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Cpu className="h-5 w-5 text-blue-500" />
                      المكونات الرئيسية
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(selectedTopic.content.components).map(([key, value]) => (
                        <div key={key} className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <p className="text-blue-700 text-sm">{value}</p>
                        </div>
                      ))}
                    </div>
                  </section>
                )}

                {/* Advantages and Disadvantages */}
                {(selectedTopic.content.advantages || selectedTopic.content.disadvantages) && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <BarChart3 className="h-5 w-5 text-green-500" />
                      المزايا والعيوب
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {selectedTopic.content.advantages && (
                        <div>
                          <h4 className="font-semibold text-green-800 mb-3">المزايا:</h4>
                          <ul className="space-y-2">
                            {selectedTopic.content.advantages.map((advantage, index) => (
                              <li key={index} className="flex items-start gap-2 text-green-700">
                                <span className="w-2 h-2 bg-green-500 rounded-full mt-2 flex-shrink-0"></span>
                                <span className="text-sm">{advantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                      {selectedTopic.content.disadvantages && (
                        <div>
                          <h4 className="font-semibold text-red-800 mb-3">العيوب:</h4>
                          <ul className="space-y-2">
                            {selectedTopic.content.disadvantages.map((disadvantage, index) => (
                              <li key={index} className="flex items-start gap-2 text-red-700">
                                <span className="w-2 h-2 bg-red-500 rounded-full mt-2 flex-shrink-0"></span>
                                <span className="text-sm">{disadvantage}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </section>
                )}

                {/* Course Review (for conclusion lesson) */}
                {selectedTopic.content.review && typeof selectedTopic.content.review === 'object' && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <BookOpen className="h-5 w-5 text-blue-500" />
                      مراجعة الدورة
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {Object.entries(selectedTopic.content.review).map(([key, value]) => (
                        <div key={key} className="p-4 bg-blue-50 rounded-lg border border-blue-200">
                          <p className="text-blue-700 text-sm">{value}</p>
                        </div>
                      ))}
                    </div>
                  </section>
                )}

                {/* Practical Projects */}
                {selectedTopic.content.practicalProjects && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Wrench className="h-5 w-5 text-orange-500" />
                      مشاريع عملية مقترحة
                    </h3>
                    <div className="space-y-3">
                      {selectedTopic.content.practicalProjects.map((project, index) => (
                        <div key={index} className="p-3 bg-orange-50 rounded-lg border border-orange-200">
                          <p className="text-orange-700">{project}</p>
                        </div>
                      ))}
                    </div>
                  </section>
                )}

                {/* Next Steps */}
                {selectedTopic.content.nextSteps && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Star className="h-5 w-5 text-purple-500" />
                      الخطوات التالية
                    </h3>
                    <div className="space-y-3">
                      {selectedTopic.content.nextSteps.map((step, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-purple-50 rounded-lg">
                          <span className="w-6 h-6 bg-purple-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                            {index + 1}
                          </span>
                          <p className="text-purple-700">{step}</p>
                        </div>
                      ))}
                    </div>
                  </section>
                )}

                {/* Safety Tips */}
                {selectedTopic.content.safetyTips && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Shield className="h-5 w-5 text-red-500" />
                      نصائح السلامة
                    </h3>
                    <div className="space-y-3">
                      {selectedTopic.content.safetyTips.map((tip, index) => (
                        <div key={index} className="flex items-start gap-3 p-3 bg-red-50 rounded-lg border border-red-200">
                          <Shield className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0" />
                          <p className="text-red-700">{tip}</p>
                        </div>
                      ))}
                    </div>
                  </section>
                )}

                {/* Applications */}
                {selectedTopic.content.applications && (
                  <section>
                    <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center gap-2">
                      <Lightbulb className="h-5 w-5 text-yellow-500" />
                      التطبيقات العملية
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                      {selectedTopic.content.applications.map((application, index) => (
                        <div key={index} className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                          <p className="text-yellow-700 text-sm">{application}</p>
                        </div>
                      ))}
                    </div>
                  </section>
                )}

                {/* Lab Experiment Section */}
                {selectedTopic.content.labExperiment && (
                  <section className="border-t-4 border-orange-500 pt-6">
                    <h2 className="text-2xl font-bold text-gray-900 mb-6 flex items-center gap-3">
                      <Beaker className="h-8 w-8 text-orange-500" />
                      التجربة المعملية
                    </h2>

                    {/* Experiment Title and Objective */}
                    <div className="bg-orange-50 rounded-lg p-6 mb-6 border border-orange-200">
                      <h3 className="text-xl font-bold text-orange-800 mb-3">
                        {selectedTopic.content.labExperiment.title}
                      </h3>
                      <div className="flex items-start gap-3">
                        <Target className="h-5 w-5 text-orange-600 mt-1 flex-shrink-0" />
                        <div>
                          <h4 className="font-semibold text-orange-700 mb-2">الهدف من التجربة:</h4>
                          <p className="text-orange-700">{selectedTopic.content.labExperiment.objective}</p>
                        </div>
                      </div>
                    </div>

                    {/* Required Equipment */}
                    <div className="bg-blue-50 rounded-lg p-6 mb-6 border border-blue-200">
                      <h4 className="text-lg font-semibold text-blue-800 mb-4 flex items-center gap-2">
                        <Wrench className="h-5 w-5" />
                        المعدات والأدوات المطلوبة
                      </h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                        {selectedTopic.content.labExperiment.requiredEquipment.map((equipment, index) => (
                          <div key={index} className="flex items-center gap-3 p-3 bg-white rounded border border-blue-200">
                            <CheckSquare className="h-4 w-4 text-blue-600 flex-shrink-0" />
                            <span className="text-blue-700 text-sm">{equipment}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Procedure */}
                    <div className="bg-green-50 rounded-lg p-6 mb-6 border border-green-200">
                      <h4 className="text-lg font-semibold text-green-800 mb-4 flex items-center gap-2">
                        <Clipboard className="h-5 w-5" />
                        خطوات التجربة
                      </h4>
                      <div className="space-y-3">
                        {selectedTopic.content.labExperiment.procedure.map((step, index) => (
                          <div key={index} className={`flex items-start gap-3 p-3 rounded ${
                            step === "" ? "hidden" : step.includes(":") && !step.includes("الجزء") ?
                            "bg-green-100 border border-green-300" :
                            step.includes("الجزء") ? "bg-green-200 border-2 border-green-400 font-semibold" :
                            "bg-white border border-green-200"
                          }`}>
                            {step !== "" && (
                              <>
                                <span className={`flex items-center justify-center w-6 h-6 rounded-full text-xs font-bold ${
                                  step.includes("الجزء") ? "bg-green-600 text-white" : "bg-green-500 text-white"
                                }`}>
                                  {step.includes("الجزء") ? "★" : index + 1}
                                </span>
                                <span className="text-green-700 text-sm leading-relaxed">{step}</span>
                              </>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Safety Precautions */}
                    <div className="bg-red-50 rounded-lg p-6 mb-6 border border-red-200">
                      <h4 className="text-lg font-semibold text-red-800 mb-4 flex items-center gap-2">
                        <AlertTriangle className="h-5 w-5" />
                        احتياطات السلامة
                      </h4>
                      <div className="space-y-3">
                        {selectedTopic.content.labExperiment.safetyPrecautions.map((precaution, index) => (
                          <div key={index} className="flex items-start gap-3 p-3 bg-white rounded border border-red-200">
                            <Shield className="h-4 w-4 text-red-600 mt-1 flex-shrink-0" />
                            <span className="text-red-700 text-sm">{precaution}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Expected Results */}
                    <div className="bg-purple-50 rounded-lg p-6 mb-6 border border-purple-200">
                      <h4 className="text-lg font-semibold text-purple-800 mb-4 flex items-center gap-2">
                        <TrendingUp className="h-5 w-5" />
                        النتائج المتوقعة
                      </h4>
                      <div className="space-y-3">
                        {Object.entries(selectedTopic.content.labExperiment.expectedResults).map(([key, value]) => (
                          <div key={key} className="p-3 bg-white rounded border border-purple-200">
                            <p className="text-purple-700 text-sm">{value}</p>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Data Sheet */}
                    {selectedTopic.content.labExperiment.dataSheet && (
                      <div className="bg-gray-50 rounded-lg p-6 border border-gray-200">
                        <h4 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                          <FileText className="h-5 w-5" />
                          جدول البيانات والنتائج
                        </h4>
                        <div className="overflow-x-auto">
                          <table className="w-full border-collapse border border-gray-300 bg-white rounded">
                            <thead>
                              <tr className="bg-gray-100">
                                {selectedTopic.content.labExperiment.dataSheet.headers.map((header, index) => (
                                  <th key={index} className="border border-gray-300 px-4 py-2 text-sm font-semibold text-gray-700">
                                    {header}
                                  </th>
                                ))}
                              </tr>
                            </thead>
                            <tbody>
                              {selectedTopic.content.labExperiment.dataSheet.sampleData.map((row, rowIndex) => (
                                <tr key={rowIndex} className="hover:bg-gray-50">
                                  {row.map((cell, cellIndex) => (
                                    <td key={cellIndex} className="border border-gray-300 px-4 py-2 text-sm text-gray-700 text-center">
                                      {cell}
                                    </td>
                                  ))}
                                </tr>
                              ))}
                            </tbody>
                          </table>
                        </div>
                        <div className="mt-4 p-3 bg-blue-50 rounded border border-blue-200">
                          <p className="text-blue-700 text-sm flex items-center gap-2">
                            <Eye className="h-4 w-4" />
                            <strong>ملاحظة:</strong> يجب على الطلاب ملء هذا الجدول بنتائجهم الفعلية أثناء التجربة
                          </p>
                        </div>
                      </div>
                    )}

                    {/* Practical Results and Applications */}
                    {selectedTopic.content.labExperiment.practicalResults && (
                      <div className="bg-gradient-to-r from-indigo-50 to-purple-50 rounded-lg p-6 border border-indigo-200">
                        <h4 className="text-lg font-semibold text-indigo-800 mb-4 flex items-center gap-2">
                          <Activity className="h-5 w-5" />
                          {selectedTopic.content.labExperiment.practicalResults.title}
                        </h4>

                        {/* Observations */}
                        {selectedTopic.content.labExperiment.practicalResults.observations && (
                          <div className="mb-6">
                            <h5 className="font-semibold text-indigo-700 mb-3">الملاحظات المتوقعة:</h5>
                            <div className="space-y-2">
                              {selectedTopic.content.labExperiment.practicalResults.observations.map((observation, index) => (
                                <div key={index} className="flex items-start gap-3 p-3 bg-white rounded border border-indigo-200">
                                  <span className="w-6 h-6 bg-indigo-500 text-white rounded-full flex items-center justify-center text-xs font-bold">
                                    {index + 1}
                                  </span>
                                  <span className="text-indigo-700 text-sm">{observation}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Calculations */}
                        {selectedTopic.content.labExperiment.practicalResults.calculations && (
                          <div className="mb-6">
                            <h5 className="font-semibold text-purple-700 mb-3">الحسابات المطلوبة:</h5>
                            <div className="space-y-2">
                              {selectedTopic.content.labExperiment.practicalResults.calculations.map((calculation, index) => (
                                <div key={index} className="flex items-start gap-3 p-3 bg-purple-50 rounded border border-purple-200">
                                  <Calculator className="h-4 w-4 text-purple-600 mt-1 flex-shrink-0" />
                                  <span className="text-purple-700 text-sm">{calculation}</span>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Applications */}
                        {Object.entries(selectedTopic.content.labExperiment.practicalResults).map(([key, value]) => {
                          if (key !== 'title' && key !== 'observations' && key !== 'calculations' && Array.isArray(value)) {
                            return (
                              <div key={key} className="mb-4">
                                <h5 className="font-semibold text-green-700 mb-3 capitalize">
                                  {key === 'temperatureSensor' && 'تطبيقات مستشعر الحرارة:'}
                                  {key === 'protectionCircuit' && 'تطبيقات دوائر الحماية:'}
                                  {key === 'filterApplications' && 'تطبيقات المرشحات:'}
                                  {key === 'powerApplications' && 'تطبيقات القدرة:'}
                                  {key === 'qualityFactors' && 'معاملات الجودة:'}
                                  {key === 'amplifierApplications' && 'تطبيقات المضخمات:'}
                                  {key === 'switchingApplications' && 'تطبيقات التحويل:'}
                                  {key === 'designConsiderations' && 'اعتبارات التصميم:'}
                                </h5>
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                  {value.map((item, index) => (
                                    <div key={index} className="p-3 bg-green-50 rounded border border-green-200">
                                      <span className="text-green-700 text-sm">{item}</span>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            );
                          }
                          return null;
                        })}
                      </div>
                    )}
                  </section>
                )}
              </div>
            )}

            {/* Default content for non-electronics topics */}
            {!isElectronicsLab && (
              <div className="space-y-6">
                <section>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Introduction</h3>
                  <p className="text-gray-700 leading-relaxed">
                    This topic covers the fundamental concepts and principles essential for understanding
                    biomedical engineering applications. You'll learn about the interdisciplinary nature
                    of the field and how engineering principles apply to medical solutions.
                  </p>
                </section>

                <section>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Key Concepts</h3>
                  <ul className="space-y-2 text-gray-700">
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <span>Biocompatibility and material selection</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <span>Regulatory compliance and safety standards</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <span>Design controls and risk management</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                      <span>Clinical applications and user requirements</span>
                    </li>
                  </ul>
                </section>

                <section>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">Practical Applications</h3>
                  <div className="bg-blue-50 rounded-lg p-6">
                    <p className="text-gray-700">
                      Understanding these concepts is crucial for developing medical devices that are
                      safe, effective, and compliant with international standards. Real-world applications
                      include prosthetics, diagnostic equipment, and therapeutic devices.
                    </p>
                  </div>
                </section>
              </div>
            )}
          </div>
        </div>

        {/* Downloads and Resources */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {isArabicContent ? 'الموارد والتحميلات' : 'Resources & Downloads'}
          </h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Download className="h-5 w-5 text-gray-500" />
                <span className="text-sm font-medium text-gray-900">
                  {isArabicContent ? 'ملخص الدرس PDF' : 'Topic Summary PDF'}
                </span>
              </div>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                {isArabicContent ? 'تحميل' : 'Download'}
              </button>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-3">
                <Download className="h-5 w-5 text-gray-500" />
                <span className="text-sm font-medium text-gray-900">
                  {isArabicContent ? 'شرائح العرض' : 'Presentation Slides'}
                </span>
              </div>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                {isArabicContent ? 'تحميل' : 'Download'}
              </button>
            </div>
            {isElectronicsLab && (
              <>
                <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg border border-orange-200">
                  <div className="flex items-center gap-3">
                    <Beaker className="h-5 w-5 text-orange-500" />
                    <span className="text-sm font-medium text-orange-800">دليل التجربة المعملية</span>
                  </div>
                  <button className="text-orange-600 hover:text-orange-700 text-sm font-medium">تحميل</button>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center gap-3">
                    <FileText className="h-5 w-5 text-green-500" />
                    <span className="text-sm font-medium text-green-800">جدول البيانات الفارغ</span>
                  </div>
                  <button className="text-green-600 hover:text-green-700 text-sm font-medium">تحميل</button>
                </div>
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-3">
                    <Calculator className="h-5 w-5 text-blue-500" />
                    <span className="text-sm font-medium text-blue-800">حاسبة المقاومات والمكثفات</span>
                  </div>
                  <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">تحميل</button>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Quick Quiz */}
        <div className="bg-white rounded-xl border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">
            {isArabicContent ? 'اختبار سريع' : 'Quick Assessment'}
          </h3>
          <p className="text-gray-600 mb-4">
            {isArabicContent
              ? 'اختبر فهمك لهذا الموضوع من خلال اختبار سريع.'
              : 'Test your understanding of this topic with a quick quiz.'
            }
          </p>
          <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
            {isArabicContent ? 'ابدأ الاختبار' : 'Take Quiz'}
          </button>
        </div>
      </div>
    );
  };

  const renderProfile = () => (
    <div className="space-y-6">
      {/* Profile Header */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center gap-6">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
            <User className="h-10 w-10 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Alex Thompson</h1>
            <p className="text-gray-600">Biomedical Engineering Student</p>
            <p className="text-sm text-gray-500">Level: {userProgress.level}</p>
          </div>
        </div>
      </div>

      {/* Academic Progress */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Academic Progress</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-600 mb-2">Course Completion</h3>
            <div className="space-y-2">
              {courses.map((course) => (
                <div key={course.id} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{course.title}</span>
                  <span className="text-sm font-medium text-blue-600">{course.progress}%</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-600 mb-2">Achievements</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-700">First Course Completed</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-700">7-Day Study Streak</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-700">Quiz Master</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Settings */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Settings</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
            <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option>English</option>
              <option>Arabic</option>
              <option>French</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email Notifications</label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" defaultChecked />
              <span className="text-sm text-gray-700">Course updates and announcements</span>
            </label>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Study Reminders</label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" defaultChecked />
              <span className="text-sm text-gray-700">Daily study reminders</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900">MedEng Learn</h1>
            </div>
            
            {/* Search Bar */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search courses, topics..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* User Menu */}
            <div className="flex items-center gap-4">
              <button className="p-2 text-gray-500 hover:text-gray-700">
                <MessageSquare className="h-5 w-5" />
              </button>
              <button className="p-2 text-gray-500 hover:text-gray-700">
                <BarChart3 className="h-5 w-5" />
              </button>
              <button 
                onClick={() => setActiveTab('profile')}
                className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
              >
                <User className="h-5 w-5 text-blue-600" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                activeTab === 'dashboard'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Home className="h-4 w-4" />
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab('courses')}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                activeTab === 'courses'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <BookOpen className="h-4 w-4" />
              Courses
            </button>
            <button
              onClick={() => setActiveTab('progress')}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                activeTab === 'progress'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <BarChart3 className="h-4 w-4" />
              Progress
            </button>
            <button
              onClick={() => {
                const essentialLabCourse = courses.find(course => course.id === 4);
                setSelectedCourse(essentialLabCourse);
                setActiveTab('course');
              }}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                selectedCourse?.id === 4 && activeTab === 'course'
                  ? 'border-orange-500 text-orange-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Zap className="h-4 w-4" />
              Essential Lab
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && renderDashboard()}
        {activeTab === 'course' && renderCourse()}
        {activeTab === 'topic' && renderTopic()}
        {activeTab === 'profile' && renderProfile()}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Platform Info */}
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <GraduationCap className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900">MedEng Learn</h3>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Medical Engineering Education Platform designed for comprehensive learning 
                and skill development in biomedical engineering.
              </p>
            </div>

            {/* Developer Info */}
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-4">Developer</h4>
              <div className="space-y-2 text-sm text-gray-600">
                <p className="font-medium">Dr. Mohammed Yagoub Esmail</p>
                <p>SUST - BME</p>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span>+249912867327</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span>+966538076790</span>
                </div>
              </div>
            </div>

            {/* Copyright */}
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-4">Copyright</h4>
              <div className="text-sm text-gray-600 space-y-2">
                <p>© 2025 Dr. Mohammed Yagoub Esmail</p>
                <p>All rights reserved.</p>
                <p className="text-xs text-gray-500">
                  This educational platform is designed to support medical engineering 
                  education and research initiatives.
                </p>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-200 mt-8 pt-6">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <p className="text-xs text-gray-500">
                MedEng Learn - Medical Engineering Education Platform
              </p>
              <p className="text-xs text-gray-500">
                Developed by Dr. Mohammed Yagoub Esmail, SUST-BME © 2025
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;