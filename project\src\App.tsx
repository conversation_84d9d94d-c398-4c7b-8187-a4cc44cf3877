import React, { useState } from 'react';
import { Search, BookOpen, User, Home, BarChart3, MessageSquare, Play, Download, CheckCircle, Clock, Star, GraduationCap, Mail, Phone } from 'lucide-react';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [selectedCourse, setSelectedCourse] = useState(null);
  const [selectedTopic, setSelectedTopic] = useState(null);

  const courses = [
    {
      id: 1,
      title: "Introduction to Medical Devices",
      level: "Bachelor's",
      progress: 65,
      duration: "8 weeks",
      description: "Comprehensive introduction to medical devices, their classification, and fundamental principles.",
      instructor: "Dr. <PERSON>",
      rating: 4.8,
      modules: [
        {
          id: 1,
          title: "Fundamentals of Medical Devices",
          topics: [
            {
              id: 1,
              title: "Introduction to Biomedical Engineering",
              completed: true,
              duration: "45 min",
              type: "video",
              summary: "Overview of biomedical engineering field, applications, and career opportunities."
            },
            {
              id: 2,
              title: "Classification of Medical Devices",
              completed: true,
              duration: "30 min",
              type: "reading",
              summary: "Understanding device classification systems, risk categories, and regulatory requirements."
            },
            {
              id: 3,
              title: "Safety Standards and Regulations",
              completed: false,
              duration: "40 min",
              type: "interactive",
              summary: "International safety standards, FDA regulations, and compliance requirements."
            }
          ]
        },
        {
          id: 2,
          title: "Diagnostic Equipment",
          topics: [
            {
              id: 4,
              title: "Medical Imaging Systems",
              completed: false,
              duration: "60 min",
              type: "video",
              summary: "X-ray, MRI, ultrasound, and CT scan technologies and their applications."
            },
            {
              id: 5,
              title: "Biosensors and Monitoring",
              completed: false,
              duration: "50 min",
              type: "lab",
              summary: "Blood glucose monitors, vital signs monitoring, and wearable devices."
            }
          ]
        }
      ]
    },
    {
      id: 2,
      title: "Biomedical Signal Processing",
      level: "Bachelor's",
      progress: 30,
      duration: "10 weeks",
      description: "Advanced signal processing techniques for biomedical applications.",
      instructor: "Prof. Michael Chen",
      rating: 4.9,
      modules: []
    },
    {
      id: 3,
      title: "Medical Device Fundamentals",
      level: "Diploma",
      progress: 80,
      duration: "6 weeks",
      description: "Basic principles of medical devices for diploma-level students.",
      instructor: "Dr. Emma Wilson",
      rating: 4.7,
      modules: []
    }
  ];

  const userProgress = {
    totalCourses: 5,
    completedCourses: 2,
    totalHours: 145,
    currentStreak: 7,
    level: "Intermediate"
  };

  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white">
        <h1 className="text-4xl font-bold mb-2">Welcome back, Alex!</h1>
        <p className="text-blue-100 text-lg">Continue your medical engineering journey</p>
      </div>

      {/* Progress Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Courses</p>
              <p className="text-2xl font-bold text-gray-900">{userProgress.totalCourses}</p>
            </div>
            <BookOpen className="h-8 w-8 text-blue-500" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Completed</p>
              <p className="text-2xl font-bold text-green-600">{userProgress.completedCourses}</p>
            </div>
            <CheckCircle className="h-8 w-8 text-green-500" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Study Hours</p>
              <p className="text-2xl font-bold text-purple-600">{userProgress.totalHours}</p>
            </div>
            <Clock className="h-8 w-8 text-purple-500" />
          </div>
        </div>
        <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg transition-shadow">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Current Streak</p>
              <p className="text-2xl font-bold text-orange-600">{userProgress.currentStreak} days</p>
            </div>
            <Star className="h-8 w-8 text-orange-500" />
          </div>
        </div>
      </div>

      {/* Current Courses */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Your Courses</h2>
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {courses.map((course) => (
            <div key={course.id} className="bg-white rounded-xl border border-gray-200 hover:shadow-lg transition-all duration-300 hover:border-blue-300">
              <div className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex-1">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">{course.title}</h3>
                    <p className="text-sm text-gray-600 mb-3">{course.description}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-500 mb-4">
                      <span className="flex items-center gap-1">
                        <GraduationCap className="h-4 w-4" />
                        {course.level}
                      </span>
                      <span className="flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {course.duration}
                      </span>
                      <span className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-400 fill-current" />
                        {course.rating}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">Progress</span>
                    <span className="text-sm font-medium text-blue-600">{course.progress}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                      style={{ width: `${course.progress}%` }}
                    ></div>
                  </div>
                </div>
                
                <button 
                  onClick={() => { setSelectedCourse(course); setActiveTab('course'); }}
                  className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  Continue Learning
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
            <CheckCircle className="h-5 w-5 text-green-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Completed "Introduction to Biomedical Engineering"</p>
              <p className="text-xs text-gray-500">2 hours ago</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
            <Play className="h-5 w-5 text-blue-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Started "Classification of Medical Devices"</p>
              <p className="text-xs text-gray-500">1 day ago</p>
            </div>
          </div>
          <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
            <MessageSquare className="h-5 w-5 text-purple-600" />
            <div>
              <p className="text-sm font-medium text-gray-900">Posted in "Medical Devices" discussion</p>
              <p className="text-xs text-gray-500">3 days ago</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );

  const renderCourse = () => (
    <div className="space-y-6">
      {/* Course Header */}
      <div className="bg-gradient-to-r from-blue-600 to-teal-600 rounded-2xl p-8 text-white">
        <button 
          onClick={() => setActiveTab('dashboard')}
          className="text-blue-100 hover:text-white mb-4 flex items-center gap-2"
        >
          ← Back to Dashboard
        </button>
        <h1 className="text-3xl font-bold mb-2">{selectedCourse?.title}</h1>
        <p className="text-blue-100 mb-4">{selectedCourse?.description}</p>
        <div className="flex items-center gap-6 text-sm">
          <span>Instructor: {selectedCourse?.instructor}</span>
          <span>Duration: {selectedCourse?.duration}</span>
          <span>Level: {selectedCourse?.level}</span>
        </div>
      </div>

      {/* Course Progress */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-bold text-gray-900">Course Progress</h2>
          <span className="text-sm font-medium text-blue-600">{selectedCourse?.progress}% Complete</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3">
          <div 
            className="bg-blue-600 h-3 rounded-full transition-all duration-300"
            style={{ width: `${selectedCourse?.progress}%` }}
          ></div>
        </div>
      </div>

      {/* Course Modules */}
      <div className="space-y-6">
        {selectedCourse?.modules.map((module) => (
          <div key={module.id} className="bg-white rounded-xl border border-gray-200">
            <div className="p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Module {module.id}: {module.title}</h3>
            </div>
            <div className="p-6">
              <div className="space-y-3">
                {module.topics.map((topic) => (
                  <div key={topic.id} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors">
                    <div className="flex items-center gap-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                        topic.completed ? 'bg-green-500' : 'bg-gray-300'
                      }`}>
                        {topic.completed && <CheckCircle className="h-4 w-4 text-white" />}
                      </div>
                      <div>
                        <h4 className="font-medium text-gray-900">{topic.title}</h4>
                        <p className="text-sm text-gray-500">{topic.duration} • {topic.type}</p>
                      </div>
                    </div>
                    <button 
                      onClick={() => { setSelectedTopic(topic); setActiveTab('topic'); }}
                      className="text-blue-600 hover:text-blue-700 font-medium"
                    >
                      {topic.completed ? 'Review' : 'Start'}
                    </button>
                  </div>
                ))}
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderTopic = () => (
    <div className="space-y-6">
      {/* Topic Header */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <button 
          onClick={() => setActiveTab('course')}
          className="text-blue-600 hover:text-blue-700 mb-4 flex items-center gap-2"
        >
          ← Back to Course
        </button>
        <h1 className="text-2xl font-bold text-gray-900 mb-2">{selectedTopic?.title}</h1>
        <p className="text-gray-600 mb-4">{selectedTopic?.summary}</p>
        <div className="flex items-center gap-4 text-sm text-gray-500">
          <span className="flex items-center gap-1">
            <Clock className="h-4 w-4" />
            {selectedTopic?.duration}
          </span>
          <span className="flex items-center gap-1 capitalize">
            <BookOpen className="h-4 w-4" />
            {selectedTopic?.type}
          </span>
        </div>
      </div>

      {/* Topic Content */}
      <div className="bg-white rounded-xl border border-gray-200 p-8">
        <div className="prose max-w-none">
          <h2 className="text-xl font-bold text-gray-900 mb-6">Content Overview</h2>
          
          {selectedTopic?.type === 'video' && (
            <div className="mb-8">
              <div className="bg-gray-900 rounded-lg aspect-video flex items-center justify-center">
                <div className="text-center text-white">
                  <Play className="h-16 w-16 mx-auto mb-4 opacity-70" />
                  <p>Video content would be embedded here</p>
                </div>
              </div>
            </div>
          )}

          <div className="space-y-6">
            <section>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Introduction</h3>
              <p className="text-gray-700 leading-relaxed">
                This topic covers the fundamental concepts and principles essential for understanding 
                biomedical engineering applications. You'll learn about the interdisciplinary nature 
                of the field and how engineering principles apply to medical solutions.
              </p>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Key Concepts</h3>
              <ul className="space-y-2 text-gray-700">
                <li className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                  <span>Biocompatibility and material selection</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                  <span>Regulatory compliance and safety standards</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                  <span>Design controls and risk management</span>
                </li>
                <li className="flex items-start gap-2">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                  <span>Clinical applications and user requirements</span>
                </li>
              </ul>
            </section>

            <section>
              <h3 className="text-lg font-semibold text-gray-900 mb-3">Practical Applications</h3>
              <div className="bg-blue-50 rounded-lg p-6">
                <p className="text-gray-700">
                  Understanding these concepts is crucial for developing medical devices that are 
                  safe, effective, and compliant with international standards. Real-world applications 
                  include prosthetics, diagnostic equipment, and therapeutic devices.
                </p>
              </div>
            </section>
          </div>
        </div>
      </div>

      {/* Downloads and Resources */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Resources & Downloads</h3>
        <div className="space-y-3">
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Download className="h-5 w-5 text-gray-500" />
              <span className="text-sm font-medium text-gray-900">Topic Summary PDF</span>
            </div>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">Download</button>
          </div>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-3">
              <Download className="h-5 w-5 text-gray-500" />
              <span className="text-sm font-medium text-gray-900">Presentation Slides</span>
            </div>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">Download</button>
          </div>
        </div>
      </div>

      {/* Quick Quiz */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Assessment</h3>
        <p className="text-gray-600 mb-4">Test your understanding of this topic with a quick quiz.</p>
        <button className="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium">
          Take Quiz
        </button>
      </div>
    </div>
  );

  const renderProfile = () => (
    <div className="space-y-6">
      {/* Profile Header */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <div className="flex items-center gap-6">
          <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center">
            <User className="h-10 w-10 text-blue-600" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Alex Thompson</h1>
            <p className="text-gray-600">Biomedical Engineering Student</p>
            <p className="text-sm text-gray-500">Level: {userProgress.level}</p>
          </div>
        </div>
      </div>

      {/* Academic Progress */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Academic Progress</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-sm font-medium text-gray-600 mb-2">Course Completion</h3>
            <div className="space-y-2">
              {courses.map((course) => (
                <div key={course.id} className="flex items-center justify-between">
                  <span className="text-sm text-gray-700">{course.title}</span>
                  <span className="text-sm font-medium text-blue-600">{course.progress}%</span>
                </div>
              ))}
            </div>
          </div>
          <div>
            <h3 className="text-sm font-medium text-gray-600 mb-2">Achievements</h3>
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-700">First Course Completed</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-700">7-Day Study Streak</span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-4 w-4 text-yellow-400 fill-current" />
                <span className="text-sm text-gray-700">Quiz Master</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Settings */}
      <div className="bg-white rounded-xl border border-gray-200 p-6">
        <h2 className="text-xl font-bold text-gray-900 mb-4">Settings</h2>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
            <select className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
              <option>English</option>
              <option>Arabic</option>
              <option>French</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Email Notifications</label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" defaultChecked />
              <span className="text-sm text-gray-700">Course updates and announcements</span>
            </label>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">Study Reminders</label>
            <label className="flex items-center gap-2">
              <input type="checkbox" className="rounded border-gray-300" defaultChecked />
              <span className="text-sm text-gray-700">Daily study reminders</span>
            </label>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white border-b border-gray-200 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <GraduationCap className="h-6 w-6 text-white" />
              </div>
              <h1 className="text-xl font-bold text-gray-900">MedEng Learn</h1>
            </div>
            
            {/* Search Bar */}
            <div className="flex-1 max-w-md mx-8">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search courses, topics..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>
            </div>

            {/* User Menu */}
            <div className="flex items-center gap-4">
              <button className="p-2 text-gray-500 hover:text-gray-700">
                <MessageSquare className="h-5 w-5" />
              </button>
              <button className="p-2 text-gray-500 hover:text-gray-700">
                <BarChart3 className="h-5 w-5" />
              </button>
              <button 
                onClick={() => setActiveTab('profile')}
                className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center"
              >
                <User className="h-5 w-5 text-blue-600" />
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            <button
              onClick={() => setActiveTab('dashboard')}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                activeTab === 'dashboard'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <Home className="h-4 w-4" />
              Dashboard
            </button>
            <button
              onClick={() => setActiveTab('courses')}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                activeTab === 'courses'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <BookOpen className="h-4 w-4" />
              Courses
            </button>
            <button
              onClick={() => setActiveTab('progress')}
              className={`flex items-center gap-2 px-1 py-4 text-sm font-medium border-b-2 ${
                activeTab === 'progress'
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              <BarChart3 className="h-4 w-4" />
              Progress
            </button>
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && renderDashboard()}
        {activeTab === 'course' && renderCourse()}
        {activeTab === 'topic' && renderTopic()}
        {activeTab === 'profile' && renderProfile()}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {/* Platform Info */}
            <div>
              <div className="flex items-center gap-3 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <GraduationCap className="h-5 w-5 text-white" />
                </div>
                <h3 className="text-lg font-bold text-gray-900">MedEng Learn</h3>
              </div>
              <p className="text-sm text-gray-600 mb-4">
                Medical Engineering Education Platform designed for comprehensive learning 
                and skill development in biomedical engineering.
              </p>
            </div>

            {/* Developer Info */}
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-4">Developer</h4>
              <div className="space-y-2 text-sm text-gray-600">
                <p className="font-medium">Dr. Mohammed Yagoub Esmail</p>
                <p>SUST - BME</p>
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  <a href="mailto:<EMAIL>" className="hover:text-blue-600 transition-colors">
                    <EMAIL>
                  </a>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span>+249912867327</span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  <span>+966538076790</span>
                </div>
              </div>
            </div>

            {/* Copyright */}
            <div>
              <h4 className="text-sm font-semibold text-gray-900 mb-4">Copyright</h4>
              <div className="text-sm text-gray-600 space-y-2">
                <p>© 2025 Dr. Mohammed Yagoub Esmail</p>
                <p>All rights reserved.</p>
                <p className="text-xs text-gray-500">
                  This educational platform is designed to support medical engineering 
                  education and research initiatives.
                </p>
              </div>
            </div>
          </div>

          {/* Bottom Bar */}
          <div className="border-t border-gray-200 mt-8 pt-6">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <p className="text-xs text-gray-500">
                MedEng Learn - Medical Engineering Education Platform
              </p>
              <p className="text-xs text-gray-500">
                Developed by Dr. Mohammed Yagoub Esmail, SUST-BME © 2025
              </p>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}

export default App;